[project]
name = "legal"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "python-docx>=1.1.0",
    "fastapi[standard]>=0.116.1",
    "minio>=7.2.15",
    "pymongo>=4.13.2",
    "pymupdf>=1.26.3",
    "argon2-cffi>=23.1.0",
    "pyjwt>=2.8.0",
    "python-multipart>=0.0.6",
    "shutup>=0.2.0",
    "tabulate>=0.9.0",
    "llama-index-core>=0.10.0",
    "pdfkit>=1.0.0",
    "selectolax>=0.3.0",
    "httpx>=0.25.0",
    "nltk>=3.8.0",
    "qdrant-client>=1.7.0",
    "llama-index-vector-stores-qdrant>=0.2.0",
    "llama-index-embeddings-openai>=0.1.0",
    "llama-index-llms-openai>=0.1.0",
    "openai>=1.0.0",
    "python-dotenv>=1.0.0",
    # LangGraph and multi-agent dependencies
    "langgraph>=0.2.0",
    "langchain>=0.3.0",
    "langchain-core>=0.3.0",
    "langchain-openai>=0.2.0",
    "langgraph-checkpoint>=2.0.0",
    "langgraph-checkpoint-postgres>=2.0.0",
    "pypdf2>=3.0.1",
    "reportlab>=4.4.2",
    "langchain-mongodb>=0.6.2",
]

[dependency-groups]
dev = [
    "ipykernel>=6.29.5",
]
