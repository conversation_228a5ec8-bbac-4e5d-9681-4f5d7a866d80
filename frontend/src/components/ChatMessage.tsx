import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Trash2, Clock, User, Bot, MoreVertical, Copy, Check } from 'lucide-react';

export interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  metadata?: any;
}

interface ChatMessageProps {
  message: Message;
  onDelete?: (messageId: string) => void;
  isDeleting?: boolean;
  showTimestamp?: boolean;
  showActions?: boolean;
}

const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  onDelete,
  isDeleting = false,
  showTimestamp = true,
  showActions = true
}) => {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [showActions, setShowActionsMenu] = useState(false);
  const [copied, setCopied] = useState(false);

  const isUser = message.role === 'user';

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(message.content);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy message:', error);
    }
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete(message.id);
      setShowDeleteConfirm(false);
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    if (days < 7) return `${days}d ago`;
    return timestamp.toLocaleDateString();
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20, scale: 0.95 }}
      transition={{ duration: 0.3 }}
      className={`group relative flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}
    >
      <div
        className={`
          relative max-w-[80%] rounded-2xl px-4 py-3 shadow-sm
          ${isUser 
            ? 'bg-blue-600 text-white ml-12' 
            : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 mr-12 border border-gray-200 dark:border-gray-700'
          }
          ${isDeleting ? 'opacity-50 pointer-events-none' : ''}
        `}
      >
        {/* Message Header */}
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            <div className={`
              w-6 h-6 rounded-full flex items-center justify-center text-xs
              ${isUser 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300'
              }
            `}>
              {isUser ? <User size={12} /> : <Bot size={12} />}
            </div>
            <span className={`text-xs font-medium ${
              isUser ? 'text-blue-100' : 'text-gray-500 dark:text-gray-400'
            }`}>
              {isUser ? 'You' : 'Legal Assistant'}
            </span>
          </div>

          {/* Actions Menu */}
          {showActions && (
            <div className="relative">
              <button
                onClick={() => setShowActionsMenu(!showActions)}
                className={`
                  opacity-0 group-hover:opacity-100 transition-opacity p-1 rounded-full
                  ${isUser 
                    ? 'hover:bg-blue-500 text-blue-100' 
                    : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-400'
                  }
                `}
              >
                <MoreVertical size={14} />
              </button>

              <AnimatePresence>
                {showActions && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.95, y: -10 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    exit={{ opacity: 0, scale: 0.95, y: -10 }}
                    className={`
                      absolute right-0 top-8 z-50 bg-white dark:bg-gray-800 
                      border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg
                      min-w-[120px] py-1
                    `}
                  >
                    <button
                      onClick={handleCopy}
                      className="w-full px-3 py-2 text-left text-sm hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center space-x-2"
                    >
                      {copied ? <Check size={14} /> : <Copy size={14} />}
                      <span>{copied ? 'Copied!' : 'Copy'}</span>
                    </button>
                    
                    {onDelete && (
                      <button
                        onClick={() => setShowDeleteConfirm(true)}
                        className="w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 flex items-center space-x-2"
                      >
                        <Trash2 size={14} />
                        <span>Delete</span>
                      </button>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          )}
        </div>

        {/* Message Content */}
        <div className="prose prose-sm max-w-none">
          <p className={`whitespace-pre-wrap ${
            isUser ? 'text-white' : 'text-gray-900 dark:text-gray-100'
          }`}>
            {message.content}
          </p>
        </div>

        {/* Timestamp */}
        {showTimestamp && (
          <div className={`
            flex items-center space-x-1 mt-2 text-xs
            ${isUser ? 'text-blue-100' : 'text-gray-400 dark:text-gray-500'}
          `}>
            <Clock size={10} />
            <span>{formatTimestamp(message.timestamp)}</span>
          </div>
        )}

        {/* Sources (for AI messages) */}
        {!isUser && message.metadata?.sources && message.metadata.sources.length > 0 && (
          <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
            <div className="text-xs text-gray-500 dark:text-gray-400 mb-2">
              Sources ({message.metadata.sources.length}):
            </div>
            <div className="space-y-1">
              {message.metadata.sources.slice(0, 3).map((source: any, index: number) => (
                <div key={index} className="text-xs bg-gray-50 dark:bg-gray-700 rounded px-2 py-1">
                  {source.source || source.filename || `Source ${index + 1}`}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      <AnimatePresence>
        {showDeleteConfirm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
            onClick={() => setShowDeleteConfirm(false)}
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-sm mx-4"
              onClick={(e) => e.stopPropagation()}
            >
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
                Delete Message
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                Are you sure you want to delete this message? This action cannot be undone.
              </p>
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  className="flex-1 px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDelete}
                  disabled={isDeleting}
                  className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isDeleting ? 'Deleting...' : 'Delete'}
                </button>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default ChatMessage;
