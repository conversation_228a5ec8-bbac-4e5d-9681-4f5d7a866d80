'use client';

import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { initializeAuth } from '@/lib/redux/slices/authSlice';
import { AppDispatch } from '@/lib/redux/store';

export default function AuthInitializer({ children }: { children: React.ReactNode }) {
  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    // Initialize auth state from localStorage on app startup
    dispatch(initializeAuth());
  }, [dispatch]);

  return <>{children}</>;
}
