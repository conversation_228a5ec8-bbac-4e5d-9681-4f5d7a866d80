'use client';

import React, { useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FileText,
  BookOpen,
  Clock,
  User,
  Loader2,
  ChevronRight,
  Info,
  Download,
  Share2,
  Database
} from 'lucide-react';

// Types
export interface SearchResult {
  id: string;
  text: string;
  score: number;
  filename?: string;
  document_id: string;
  page_number?: number;
  chunk_index: number;
  metadata: Record<string, any>;
}

export interface RetrieveResponse {
  id: string;
  score: number;
  text: string;
  metadata: Record<string, any>;
}

// Props interface
interface DocumentsBrowserProps {
  searchResults: SearchResult[];
  sourceNodes: RetrieveResponse[];
  searchQuery: string;
  isSearching: boolean;
  hasMore: boolean;
  onLoadMore: () => void;
}

const DocumentsBrowser: React.FC<DocumentsBrowserProps> = ({
  searchResults,
  sourceNodes,
  searchQuery,
  isSearching,
  hasMore,
  onLoadMore
}) => {
  const loadMoreRef = useRef<HTMLDivElement>(null);

  // Intersection Observer for infinite scroll
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        const target = entries[0];
        if (target.isIntersecting && hasMore && !isSearching) {
          onLoadMore();
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px'
      }
    );

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current);
    }

    return () => {
      if (loadMoreRef.current) {
        observer.unobserve(loadMoreRef.current);
      }
    };
  }, [hasMore, isSearching, onLoadMore]);

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('ne-NP', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return 'Unknown date';
    }
  };

  // Deduplicate data to prevent duplicate keys
  const deduplicateById = <T extends { id: string }>(items: T[]): T[] => {
    const seen = new Set<string>();
    return items.filter(item => {
      if (seen.has(item.id)) {
        return false;
      }
      seen.add(item.id);
      return true;
    });
  };

  // Apply deduplication to both datasets
  const deduplicatedSourceNodes = deduplicateById(sourceNodes);
  const deduplicatedSearchResults = deduplicateById(searchResults);

  const displayData = deduplicatedSourceNodes.length > 0 ? deduplicatedSourceNodes : deduplicatedSearchResults;
  const isShowingSourceNodes = deduplicatedSourceNodes.length > 0;

  return (
    <div className="w-full h-full flex flex-col">
      {/* Documents Header */}
      <div className="flex-shrink-0 p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-emerald-50 to-green-50 dark:from-gray-800 dark:to-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: "spring", stiffness: 260, damping: 20 }}
              className="w-12 h-12 bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl flex items-center justify-center shadow-lg"
            >
              <BookOpen className="h-7 w-7 text-white" />
            </motion.div>
            <div>
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                {isShowingSourceNodes ? 'स्रोत नोडहरू' : 'सम्बन्धित दस्तावेजहरू'}
              </h2>
              <p className="text-sm text-gray-600 dark:text-gray-300 flex items-center space-x-2">
                <span>
                  {isShowingSourceNodes 
                    ? `${sourceNodes.length} स्रोत नोडहरू फेला परे`
                    : `${searchResults.length} दस्तावेजहरू फेला परे`
                  }
                </span>
                {searchQuery && (
                  <>
                    <span>•</span>
                    <span className="font-medium">"{searchQuery}"</span>
                  </>
                )}
              </p>
            </div>
          </div>
          {isSearching && (
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            >
              <Loader2 className="h-6 w-6 text-emerald-500" />
            </motion.div>
          )}
        </div>
      </div>

      {/* Documents List - Fixed Height with Scroll */}
      <div
        className="flex-1 overflow-y-auto p-6 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-800"
      >
        <div className="space-y-6">
          <AnimatePresence mode="popLayout">
            {/* Show Source Nodes if available */}
            {isShowingSourceNodes ? (
              deduplicatedSourceNodes.map((node, index) => (
                <motion.div
                  key={`source-node-${node.id}-${index}`}
                  initial={{ opacity: 0, y: 30, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: -30, scale: 0.95 }}
                  transition={{
                    type: "spring",
                    stiffness: 300,
                    damping: 30,
                    delay: index * 0.05
                  }}
                  whileHover={{
                    scale: 1.02,
                    y: -4,
                    transition: { type: "spring", stiffness: 400, damping: 25 }
                  }}
                  className="bg-white dark:bg-gray-800 rounded-3xl p-6 shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300 cursor-pointer group"
                >
                  {/* Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <motion.div
                        whileHover={{ rotate: 5 }}
                        className="w-10 h-10 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-md"
                      >
                        <Database className="h-5 w-5 text-white" />
                      </motion.div>
                      <div>
                        <h3 className="font-bold text-gray-900 dark:text-white text-base group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors">
                          स्रोत नोड #{index + 1}
                        </h3>
                        <div className="flex items-center space-x-3 text-xs text-gray-500 dark:text-gray-400 mt-1">
                          <span>ID: {node.id.substring(0, 8)}...</span>
                        </div>
                      </div>
                    </div>

                    {/* Score Badge */}
                    <motion.div
                      whileHover={{ scale: 1.1 }}
                      className="px-3 py-1 bg-gradient-to-r from-purple-500 to-indigo-600 text-white text-xs font-bold rounded-full shadow-md"
                    >
                      {(node.score * 100).toFixed(1)}%
                    </motion.div>
                  </div>

                  {/* Content */}
                  <div className="mb-4">
                    <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed">
                      {node.text}
                    </p>
                  </div>

                  {/* Metadata */}
                  <div className="flex items-center justify-between pt-4 border-t border-gray-100 dark:border-gray-700">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
                        <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                        <span>Source Node</span>
                      </div>
                      {node.metadata?.filename && (
                        <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          <span>{node.metadata.filename}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))
            ) : (
              /* Show Search Results */
              deduplicatedSearchResults.map((result, index) => (
                <motion.div
                  key={`search-result-${result.id}-${index}`}
                  initial={{ opacity: 0, y: 30, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  exit={{ opacity: 0, y: -30, scale: 0.95 }}
                  transition={{
                    type: "spring",
                    stiffness: 300,
                    damping: 30,
                    delay: index * 0.05
                  }}
                  whileHover={{
                    scale: 1.02,
                    y: -4,
                    transition: { type: "spring", stiffness: 400, damping: 25 }
                  }}
                  className="bg-white dark:bg-gray-800 rounded-3xl p-6 shadow-lg border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300 cursor-pointer group"
                >
                  {/* Header */}
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <motion.div
                        whileHover={{ rotate: 5 }}
                        className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-md"
                      >
                        <FileText className="h-5 w-5 text-white" />
                      </motion.div>
                      <div>
                        <h3 className="font-bold text-gray-900 dark:text-white text-base group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                          {result.filename || result.metadata?.filename || 'Unknown Document'}
                        </h3>
                        <div className="flex items-center space-x-3 text-xs text-gray-500 dark:text-gray-400 mt-1">
                          <div className="flex items-center space-x-1">
                            <Clock className="h-3 w-3" />
                            <span>{result.metadata?.uploaded_at ? formatDate(result.metadata.uploaded_at) : 'Unknown date'}</span>
                          </div>
                          <span>•</span>
                          <div className="flex items-center space-x-1">
                            <User className="h-3 w-3" />
                            <span>{result.metadata?.uploaded_by || 'Unknown user'}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Score Badge */}
                    <motion.div
                      whileHover={{ scale: 1.1 }}
                      className="px-3 py-1 bg-gradient-to-r from-emerald-500 to-green-600 text-white text-xs font-bold rounded-full shadow-md"
                    >
                      {(result.score * 100).toFixed(1)}%
                    </motion.div>
                  </div>

                  {/* Content */}
                  <div className="mb-4">
                    <p className="text-sm text-gray-700 dark:text-gray-300 leading-relaxed line-clamp-4">
                      {result.text}
                    </p>
                  </div>

                  {/* Metadata */}
                  <div className="flex items-center justify-between pt-4 border-t border-gray-100 dark:border-gray-700">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span>Chunk {result.chunk_index || 0}</span>
                      </div>
                      <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
                        <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                        <span className="capitalize">{result.metadata?.language || 'unknown'}</span>
                      </div>
                      <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
                        <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                        <span className="capitalize">{result.metadata?.type?.replace('_', ' ') || 'document'}</span>
                      </div>
                    </div>

                    {/* Action Icons */}
                    <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        className="p-2 text-gray-400 hover:text-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                      >
                        <Info className="h-4 w-4" />
                      </motion.button>
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        className="p-2 text-gray-400 hover:text-emerald-500 hover:bg-emerald-50 dark:hover:bg-emerald-900/20 rounded-lg transition-colors"
                      >
                        <Download className="h-4 w-4" />
                      </motion.button>
                      <motion.button
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                        className="p-2 text-gray-400 hover:text-purple-500 hover:bg-purple-50 dark:hover:bg-purple-900/20 rounded-lg transition-colors"
                      >
                        <Share2 className="h-4 w-4" />
                      </motion.button>
                      <motion.div
                        whileHover={{ x: 5 }}
                        className="p-2"
                      >
                        <ChevronRight className="h-4 w-4 text-gray-400" />
                      </motion.div>
                    </div>
                  </div>
                </motion.div>
              ))
            )}
          </AnimatePresence>

          {/* Infinite Scroll Trigger */}
          {hasMore && !isShowingSourceNodes && (
            <div ref={loadMoreRef} className="py-8 flex justify-center">
              {isSearching ? (
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  className="flex items-center space-x-3 text-emerald-600 dark:text-emerald-400"
                >
                  <Loader2 className="h-6 w-6" />
                  <span className="text-sm font-medium">थप दस्तावेजहरू लोड गर्दै...</span>
                </motion.div>
              ) : (
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  className="px-6 py-3 bg-gradient-to-r from-emerald-500 to-green-600 text-white rounded-full shadow-lg cursor-pointer"
                  onClick={onLoadMore}
                >
                  <span className="text-sm font-medium">थप दस्तावेजहरू लोड गर्नुहोस्</span>
                </motion.div>
              )}
            </div>
          )}

          {/* No Results Message */}
          {displayData.length === 0 && !isSearching && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-center py-12"
            >
              <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                <FileText className="h-8 w-8 text-gray-400" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                कुनै दस्तावेज फेला परेन
              </h3>
              <p className="text-gray-500 dark:text-gray-400">
                कृपया फरक खोजशब्द प्रयोग गरेर फेरि प्रयास गर्नुहोस्।
              </p>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DocumentsBrowser;
