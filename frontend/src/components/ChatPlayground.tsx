'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Send,
  Bot,
  User,
  Loader2,
  Database,
  MessageCircle,
  Brain
} from 'lucide-react';

// Types
export type PlaygroundMode = 'retrieve' | 'query' | 'agent';

export interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
}

export interface RetrieveResponse {
  id: string;
  score: number;
  text: string;
  metadata: Record<string, any>;
}

export interface QueryResponse {
  query: string;
  response: string;
  source_nodes: RetrieveResponse[];
}

// Import uniform response types
import { UniformResponse, SourceGroup, SentenceData } from '@/lib/services/agentService';

export interface AgentResponse {
  agent_type: string;
  agent_name: string;
  status: string;
  content: string;
  uniform_sources?: UniformResponse;  // New uniform format
  sources: RetrieveResponse[];  // Legacy format for backward compatibility
  metadata: Record<string, any>;
  processing_time_ms?: number;
  session_id?: string;
}

// Props interface
interface ChatPlaygroundProps {
  messages: Message[];
  inputMessage: string;
  isLoading: boolean;
  playgroundMode: PlaygroundMode;
  onSendMessage: () => void;
  onInputChange: (value: string) => void;
  onModeChange: (mode: PlaygroundMode) => void;
  onSourceNodesUpdate: (nodes: RetrieveResponse[]) => void;
  onQueryResponseUpdate: (response: string) => void;
  onAgentResponseUpdate?: (response: AgentResponse) => void;
  onMessageSelect?: (messageId: string, messageType: 'user' | 'assistant') => void;
  selectedMessageId?: string | null;
}

const ChatPlayground: React.FC<ChatPlaygroundProps> = ({
  messages,
  inputMessage,
  isLoading,
  playgroundMode,
  onSendMessage,
  onInputChange,
  onModeChange,
  onSourceNodesUpdate,
  onQueryResponseUpdate,
  onAgentResponseUpdate,
  onMessageSelect,
  selectedMessageId
}) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      onSendMessage();
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('ne-NP', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="w-full h-full flex flex-col">
      {/* Chat Header */}
      <div className="flex-shrink-0 p-6 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: "spring", stiffness: 260, damping: 20 }}
              className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg"
            >
              <Bot className="h-7 w-7 text-white" />
            </motion.div>
            <div>
              <h2 className="text-xl font-bold text-gray-900 dark:text-white">कानुनी सहायक</h2>
              <div className="text-sm text-gray-600 dark:text-gray-300 flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span>AI-powered Legal Assistant</span>
              </div>
            </div>
          </div>
          
          {/* Mode Toggle */}
          <div className="flex items-center space-x-3 bg-white dark:bg-gray-700 rounded-lg p-1 shadow-sm">
            <button
              onClick={() => onModeChange('retrieve')}
              className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-all ${
                playgroundMode === 'retrieve'
                  ? 'bg-blue-500 text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600'
              }`}
            >
              <Database className="h-4 w-4" />
              <span>Retrieve</span>
            </button>
            <button
              onClick={() => onModeChange('query')}
              className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-all ${
                playgroundMode === 'query'
                  ? 'bg-blue-500 text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600'
              }`}
            >
              <MessageCircle className="h-4 w-4" />
              <span>Query</span>
            </button>
            <button
              onClick={() => onModeChange('agent')}
              className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-all ${
                playgroundMode === 'agent'
                  ? 'bg-purple-500 text-white shadow-sm'
                  : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-600'
              }`}
            >
              <Brain className="h-4 w-4" />
              <span>Agent</span>
            </button>
          </div>
        </div>
      </div>

      {/* Chat Messages - Flexible Height with Scroll */}
      <div
        ref={chatContainerRef}
        className="flex-1 overflow-y-auto p-6 space-y-4 bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-800"
      >
        <AnimatePresence mode="popLayout">
          {messages.map((message, index) => (
            <motion.div
              key={message.id}
              initial={{ opacity: 0, y: 20, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -20, scale: 0.95 }}
              transition={{
                type: "spring",
                stiffness: 300,
                damping: 30,
                delay: index * 0.05
              }}
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div className={`flex items-start space-x-3 max-w-[80%] ${message.role === 'user' ? 'flex-row-reverse space-x-reverse' : ''}`}>
                {/* Avatar */}
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  className={`w-10 h-10 rounded-2xl flex items-center justify-center shadow-md ${
                    message.role === 'user'
                      ? 'bg-gradient-to-br from-emerald-500 to-green-600'
                      : 'bg-gradient-to-br from-blue-500 to-indigo-600'
                  }`}
                >
                  {message.role === 'user' ? (
                    <User className="h-5 w-5 text-white" />
                  ) : (
                    <Bot className="h-5 w-5 text-white" />
                  )}
                </motion.div>

                {/* Message Content */}
                <div className={`flex flex-col space-y-2 ${message.role === 'user' ? 'items-end' : 'items-start'}`}>
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    onClick={() => onMessageSelect && onMessageSelect(message.id, message.role)}
                    className={`px-6 py-4 rounded-3xl shadow-lg cursor-pointer transition-all ${
                      message.role === 'user'
                        ? 'bg-gradient-to-br from-emerald-500 to-green-600 text-white'
                        : `bg-white dark:bg-gray-700 text-gray-900 dark:text-white border border-gray-200 dark:border-gray-600 ${
                            selectedMessageId === message.id ? 'ring-2 ring-blue-500 ring-opacity-50' : ''
                          }`
                    } ${
                      message.role === 'assistant' ? 'hover:border-blue-300 dark:hover:border-blue-500' : ''
                    }`}
                  >
                    <p className="text-sm leading-relaxed whitespace-pre-wrap">
                      {message.content}
                    </p>
                    {/* Source indicator for assistant messages */}
                    {message.role === 'assistant' && (
                      <div className="mt-2 pt-2 border-t border-gray-200 dark:border-gray-600">
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          Click to view sources
                        </span>
                      </div>
                    )}
                  </motion.div>
                  <span className="text-xs text-gray-500 dark:text-gray-400 px-2">
                    {formatTime(message.timestamp)}
                  </span>
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>

        {/* Loading Indicator */}
        {isLoading && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="flex justify-start"
          >
            <div className="flex items-start space-x-3 max-w-[80%]">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-md">
                <Bot className="h-5 w-5 text-white" />
              </div>
              <div className="bg-white dark:bg-gray-700 px-6 py-4 rounded-3xl shadow-lg border border-gray-200 dark:border-gray-600">
                <div className="flex items-center space-x-2">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  >
                    <Loader2 className="h-4 w-4 text-blue-500" />
                  </motion.div>
                  <span className="text-sm text-gray-600 dark:text-gray-300">
                    {playgroundMode === 'retrieve'
                      ? 'स्रोत नोडहरू खोज्दै...'
                      : playgroundMode === 'agent'
                      ? 'एजेन्ट प्रक्रिया गर्दै...'
                      : 'जवाफ तयार गर्दै...'}
                  </span>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Auto-scroll anchor */}
        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="flex-shrink-0 p-6 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
        <div className="flex items-end space-x-4">
          <div className="flex-1 relative">
            <textarea
              value={inputMessage}
              onChange={(e) => onInputChange(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={
                playgroundMode === 'retrieve'
                  ? "स्रोत नोडहरू खोज्नुहोस्..."
                  : playgroundMode === 'agent'
                  ? "एजेन्ट सिस्टमसँग कुराकानी गर्नुहोस्..."
                  : "तपाईंको कानुनी प्रश्न सोध्नुहोस्..."
              }
              className="w-full px-4 py-3 pr-12 bg-gray-50 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
              rows={3}
              disabled={isLoading}
            />
          </div>
          <motion.button
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={onSendMessage}
            disabled={!inputMessage.trim() || isLoading}
            className="p-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Send className="h-5 w-5" />
          </motion.button>
        </div>
      </div>
    </div>
  );
};

export default ChatPlayground;
