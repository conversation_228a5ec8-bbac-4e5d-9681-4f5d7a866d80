import { httpClient } from './httpClient';

// Types
export interface ChatThread {
  thread_id: string;
  user_id: string;
  title: string;
  description?: string;
  created_at: string;
  updated_at: string;
  last_activity: string;
  status: 'active' | 'archived' | 'deleted';
  is_pinned: boolean;
  tags: string[];
  context_type?: string;
  knowledge_base?: string;
  retriever_settings: Record<string, any>;
  langchain_session_id: string;
  message_count: number;
  ai_message_count: number;
  user_message_count: number;
}

export interface CreateThreadRequest {
  title: string;
  description?: string;
  context_type?: string;
  knowledge_base?: string;
  tags?: string[];
}

export interface UpdateThreadRequest {
  title?: string;
  description?: string;
  status?: 'active' | 'archived' | 'deleted';
  is_pinned?: boolean;
  tags?: string[];
}

export interface SendMessageRequest {
  thread_id: string;
  content: string;
  max_results?: number;
  include_sources?: boolean;
  metadata?: Record<string, any>;
}

export interface MessageResponse {
  id: string;
  content: string;
  type: 'human' | 'ai';
  timestamp: string;
  additional_kwargs: Record<string, any>;
}

export interface ChatResponse {
  thread_id: string;
  message_id: string;
  user_message: string;
  ai_response: string;
  sources: any[];
  processing_time_ms: number;
  timestamp: string;
}

export interface ThreadListResponse {
  threads: ChatThread[];
  total_count: number;
  page: number;
  page_size: number;
  has_more: boolean;
}

export interface MessageListResponse {
  messages: MessageResponse[];
  total_count: number;
  thread_id: string;
  has_more: boolean;
}

export interface ChatHistoryStats {
  total_threads: number;
  active_threads: number;
  total_messages: number;
  total_ai_responses: number;
  avg_messages_per_thread: number;
  most_active_thread?: string;
  recent_activity_count: number;
}

class ChatHistoryService {
  // Thread management
  async createThread(request: CreateThreadRequest): Promise<ChatThread> {
    return httpClient.post<ChatThread>('/chat-history/threads', request);
  }

  async listThreads(params: {
    limit?: number;
    offset?: number;
    status?: 'active' | 'archived' | 'deleted';
  } = {}): Promise<ThreadListResponse> {
    return httpClient.get<ThreadListResponse>('/chat-history/threads', { params });
  }

  async getThread(threadId: string): Promise<ChatThread> {
    return httpClient.get<ChatThread>(`/chat-history/threads/${threadId}`);
  }

  async updateThread(threadId: string, request: UpdateThreadRequest): Promise<ChatThread> {
    return httpClient.put<ChatThread>(`/chat-history/threads/${threadId}`, request);
  }

  async deleteThread(threadId: string, permanent: boolean = false): Promise<{ message: string; permanent: boolean }> {
    return httpClient.delete(`/chat-history/threads/${threadId}?permanent=${permanent}`);
  }

  // Message management
  async sendMessage(request: SendMessageRequest): Promise<ChatResponse> {
    const { thread_id, ...messageData } = request;
    return httpClient.post<ChatResponse>(`/chat-history/threads/${thread_id}/messages`, messageData);
  }

  async getMessages(threadId: string, params: {
    limit?: number;
    offset?: number;
  } = {}): Promise<MessageListResponse> {
    return httpClient.get<MessageListResponse>(`/chat-history/threads/${threadId}/messages`, { params });
  }

  async clearThreadHistory(threadId: string): Promise<{ message: string }> {
    return httpClient.delete(`/chat-history/threads/${threadId}/messages`);
  }

  // Thread actions
  async archiveThread(threadId: string): Promise<{ message: string }> {
    return httpClient.post(`/chat-history/threads/${threadId}/archive`);
  }

  async unarchiveThread(threadId: string): Promise<{ message: string }> {
    return httpClient.post(`/chat-history/threads/${threadId}/unarchive`);
  }

  async pinThread(threadId: string): Promise<{ message: string }> {
    return httpClient.post(`/chat-history/threads/${threadId}/pin`);
  }

  async unpinThread(threadId: string): Promise<{ message: string }> {
    return httpClient.post(`/chat-history/threads/${threadId}/unpin`);
  }

  // Search and statistics
  async searchThreads(params: {
    query?: string;
    tags?: string;
    context_type?: string;
    limit?: number;
    offset?: number;
  } = {}): Promise<ThreadListResponse> {
    return httpClient.get<ThreadListResponse>('/chat-history/threads/search', { params });
  }

  async getChatStats(): Promise<ChatHistoryStats> {
    return httpClient.get<ChatHistoryStats>('/chat-history/stats');
  }

  // Utility methods
  formatThreadTitle(thread: ChatThread): string {
    return thread.title || `Thread ${thread.thread_id.substring(0, 8)}`;
  }

  formatLastActivity(thread: ChatThread): string {
    const date = new Date(thread.last_activity);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  }

  getThreadStatusColor(status: string): string {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-100';
      case 'archived': return 'text-yellow-600 bg-yellow-100';
      case 'deleted': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  }

  getContextTypeIcon(contextType?: string): string {
    switch (contextType) {
      case 'legal': return '⚖️';
      case 'general': return '💬';
      case 'research': return '🔍';
      default: return '📝';
    }
  }

  // Batch operations
  async batchUpdateThreads(threadIds: string[], updates: UpdateThreadRequest): Promise<ChatThread[]> {
    const promises = threadIds.map(id => this.updateThread(id, updates));
    return Promise.all(promises);
  }

  async batchDeleteThreads(threadIds: string[], permanent: boolean = false): Promise<void> {
    const promises = threadIds.map(id => this.deleteThread(id, permanent));
    await Promise.all(promises);
  }

  // Export/Import (placeholder for future implementation)
  async exportThread(threadId: string, format: 'json' | 'csv' | 'txt' = 'json'): Promise<Blob> {
    // This would be implemented when the backend supports export
    throw new Error('Export functionality not yet implemented');
  }

  async importThread(data: any): Promise<ChatThread> {
    // This would be implemented when the backend supports import
    throw new Error('Import functionality not yet implemented');
  }
}

export const chatHistoryService = new ChatHistoryService();
export default chatHistoryService;
