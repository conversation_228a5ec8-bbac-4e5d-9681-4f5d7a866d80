import { httpClient } from './httpClient';
import { store } from '@/lib/redux/store';

// Types
interface SearchAllRequest {
  limit?: number;
  offset?: string;
  document_type?: string;
  document_id?: string;
}

interface SearchResult {
  id: string;
  content: string;
  metadata: Record<string, any>;
  score?: number;
}

interface SearchAllResponse {
  next_offset?: string;
  response: SearchResult[];
  has_more: boolean;
  total_count: number;
  processing_time_ms: number;
}

interface RetrieveResponse {
  id: string;
  score: number;
  text: string;
  metadata: Record<string, any>;
}

interface QueryResponse {
  query: string;
  response: string;
  source_nodes: RetrieveResponse[];
}

interface Document {
  id: string;
  filename: string;
  content_type: string;
  size: number;
  upload_date: string;
  metadata?: Record<string, any>;
}

class KnowledgeBaseService {
  // Custom upload method with progress tracking
  private async uploadWithProgress<T>(endpoint: string, formData: FormData, onProgress?: (progress: number) => void): Promise<T> {
    if (!onProgress) {
      // If no progress tracking needed, use the httpClient directly
      return httpClient.request<T>(endpoint, {
        method: 'POST',
        body: formData,
        headers: {} // Let browser set content-type with boundary
      });
    }

    // For progress tracking, we need to use XMLHttpRequest
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();

      xhr.upload.addEventListener('progress', (e) => {
        if (e.lengthComputable) {
          const percentComplete = (e.loaded / e.total) * 100;
          onProgress(percentComplete);
        }
      });

      xhr.addEventListener('load', () => {
        if (xhr.status >= 200 && xhr.status < 300) {
          try {
            const response = JSON.parse(xhr.responseText);
            resolve(response);
          } catch (error) {
            reject(new Error('Invalid JSON response'));
          }
        } else {
          try {
            const errorData = JSON.parse(xhr.responseText);
            reject(new Error(errorData.detail || `HTTP ${xhr.status}: ${xhr.statusText}`));
          } catch (error) {
            reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
          }
        }
      });

      xhr.addEventListener('error', () => {
        reject(new Error('Network error occurred'));
      });

      // Get the base URL
      const baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/v1';
      const url = `${baseURL}${endpoint}`;
      xhr.open('POST', url);

      // Get auth token from Redux store
      try {
        const state = store.getState();
        const token = state.auth?.token;
        
        if (token) {
          xhr.setRequestHeader('Authorization', `Bearer ${token}`);
        }
      } catch (error) {
        console.warn('Could not get auth token for upload:', error);
      }

      xhr.send(formData);
    });
  }

  // Knowledge Base methods
  async searchAllDocuments(params: SearchAllRequest): Promise<SearchAllResponse> {
    return httpClient.get<SearchAllResponse>('/knowledge-base/search_all', { params: params as any });
  }

  async uploadDocument(file: File, onProgress?: (progress: number) => void): Promise<Document> {
    const formData = new FormData();
    formData.append('file', file);
    
    return this.uploadWithProgress<Document>('/knowledge-base/upload', formData, onProgress);
  }

  async uploadDocuments(files: File[], onProgress?: (progress: number) => void): Promise<Document[]> {
    const formData = new FormData();
    files.forEach((file) => {
      formData.append(`file`, file);
    });
    
    return this.uploadWithProgress<Document[]>('/knowledge-base/upload-multiple', formData, onProgress);
  }

  async getDocuments(): Promise<Document[]> {
    return httpClient.get<Document[]>('/knowledge-base/documents');
  }

  async deleteDocument(documentId: string): Promise<void> {
    await httpClient.delete(`/knowledge-base/documents/${documentId}`);
  }

  async getDocument(documentId: string): Promise<Document> {
    return httpClient.get<Document>(`/knowledge-base/documents/${documentId}`);
  }

  async updateDocumentMetadata(documentId: string, metadata: Record<string, any>): Promise<Document> {
    return httpClient.patch<Document>(`/knowledge-base/documents/${documentId}`, { metadata });
  }

  // New retrieve and query methods
  async retrieveSourceNodes(query: string, maxResults: number = 5): Promise<RetrieveResponse[]> {
    return httpClient.get<RetrieveResponse[]>('/knowledge-base/retrieve', {
      params: { query, max_results: maxResults }
    });
  }

  async queryWithEngine(query: string): Promise<QueryResponse> {
    return httpClient.get<QueryResponse>('/knowledge-base/query', {
      params: { query }
    });
  }
}

export const knowledgeBaseService = new KnowledgeBaseService();
export default knowledgeBaseService;
export { KnowledgeBaseService };
export type { RetrieveResponse, QueryResponse };
