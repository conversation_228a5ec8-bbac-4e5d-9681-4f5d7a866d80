import { httpClient } from './httpClient';

// Types
interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: string;
  conversation_id?: string;
}

interface Conversation {
  id: string;
  title: string;
  created_at: string;
  updated_at: string;
  message_count: number;
}

interface SendMessageRequest {
  message: string;
  conversation_id?: string;
  context?: string;
  temperature?: number;
  max_tokens?: number;
}

interface SendMessageResponse {
  message: Message;
  conversation_id: string;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

interface ChatSettings {
  temperature: number;
  max_tokens: number;
  model: string;
  system_prompt?: string;
}

class PlaygroundService {
  // Chat/Message methods
  async sendMessage(request: SendMessageRequest): Promise<SendMessageResponse> {
    return httpClient.post<SendMessageResponse>('/chat/message', request);
  }

  async getConversations(): Promise<Conversation[]> {
    return httpClient.get<Conversation[]>('/chat/conversations');
  }

  async getConversation(conversationId: string): Promise<Conversation> {
    return httpClient.get<Conversation>(`/chat/conversations/${conversationId}`);
  }

  async getConversationMessages(conversationId: string): Promise<Message[]> {
    return httpClient.get<Message[]>(`/chat/conversations/${conversationId}/messages`);
  }

  async createConversation(title?: string): Promise<Conversation> {
    return httpClient.post<Conversation>('/chat/conversations', { title });
  }

  async updateConversation(conversationId: string, title: string): Promise<Conversation> {
    return httpClient.patch<Conversation>(`/chat/conversations/${conversationId}`, { title });
  }

  async deleteConversation(conversationId: string): Promise<void> {
    await httpClient.delete(`/chat/conversations/${conversationId}`);
  }

  async deleteMessage(messageId: string): Promise<void> {
    await httpClient.delete(`/chat/messages/${messageId}`);
  }

  // Settings methods
  async getChatSettings(): Promise<ChatSettings> {
    return httpClient.get<ChatSettings>('/chat/settings');
  }

  async updateChatSettings(settings: Partial<ChatSettings>): Promise<ChatSettings> {
    return httpClient.patch<ChatSettings>('/chat/settings', settings);
  }

  // Model methods
  async getAvailableModels(): Promise<string[]> {
    return httpClient.get<string[]>('/chat/models');
  }

  // Stream message (for real-time chat)
  async streamMessage(request: SendMessageRequest, onChunk: (chunk: string) => void): Promise<void> {
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/v1'}/chat/stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.getAuthToken()}`
      },
      body: JSON.stringify(request)
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('No response body');
    }

    const decoder = new TextDecoder();
    
    try {
      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;
        
        const chunk = decoder.decode(value);
        const lines = chunk.split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') {
              return;
            }
            
            try {
              const parsed = JSON.parse(data);
              if (parsed.content) {
                onChunk(parsed.content);
              }
            } catch (error) {
              console.warn('Failed to parse SSE data:', data);
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
  }

  private getAuthToken(): string | null {
    try {
      const { store } = require('@/lib/redux/store');
      const state = store.getState();
      return state.auth?.token || null;
    } catch (error) {
      console.warn('Could not get auth token:', error);
      return null;
    }
  }
}

export const playgroundService = new PlaygroundService();
export default playgroundService;
