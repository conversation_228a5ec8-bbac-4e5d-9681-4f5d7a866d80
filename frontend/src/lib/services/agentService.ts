import { httpClient } from './httpClient';

// Types for agent system
export interface AgentChatRequest {
  query: string;
  conversation_id?: string;
  session_id?: string;
  language?: string;
  max_results?: number;
  include_sources?: boolean;
  agent_name?: string;
}

// New uniform response types
export interface SentenceData {
  sentence: string;
  page_number: number;
  sent_id: string;
  confidence_score?: number;
}

export interface SourceGroup {
  source_name: string;
  sentences: SentenceData[];
  minio_path?: string;
  presigned_url?: string;
}

export interface UniformResponse {
  sources: SourceGroup[];
  total_sources_found: number;
  processing_time_ms?: number;
}

// Legacy source reference for backward compatibility
export interface SourceReference {
  document_id: string;
  filename: string;
  page_number?: number;
  chunk_id?: string;
  text_snippet: string;
  confidence_score?: number;
  minio_path?: string;
  presigned_url?: string;
  coordinates?: Record<string, any>;
}

export interface AgentChatResponse {
  agent_type: string;
  agent_name: string;
  status: string;
  content: string;
  uniform_sources?: UniformResponse;  // New uniform format
  sources: SourceReference[];  // Legacy format for backward compatibility
  metadata: Record<string, any>;
  processing_time_ms?: number;
  conversation_id?: string;
  session_id?: string;
}

export interface AgentInfo {
  name: string;
  type: string;
  status: string;
  timeout_seconds?: number;
  max_retries?: number;
}

export interface AgentHealthResponse {
  system_status: string;
  total_agents: number;
  agents_by_type: Record<string, number>;
  agents: Record<string, Record<string, any>>;
  memory_status: Record<string, any>;
}

export interface UserSession {
  session_id: string;
  user_id: string;
  tenant_id: string;
  conversation_id?: string;
  created_at: string;
  last_updated: string;
  active: boolean;
}

export interface UserContext {
  user_id: string;
  tenant_id: string;
  preferences: Record<string, any>;
  conversation_summaries: Array<{
    summary: string;
    timestamp: string;
  }>;
  learned_topics: string[];
  language_preference: string;
  interaction_count: number;
  last_updated?: string;
  created_at?: string;
}

class AgentService {
  // Chat methods
  async sendToAgent(request: AgentChatRequest): Promise<AgentChatResponse> {
    return httpClient.post<AgentChatResponse>('/agents/chat', request);
  }

  async getAgentHealth(): Promise<AgentHealthResponse> {
    return httpClient.get<AgentHealthResponse>('/agents/health');
  }

  async listAgents(): Promise<{ agents: AgentInfo[]; total_count: number }> {
    return httpClient.get<{ agents: AgentInfo[]; total_count: number }>('/agents/list');
  }

  async getAgentInfo(agentName: string): Promise<AgentInfo> {
    return httpClient.get<AgentInfo>(`/agents/info/${agentName}`);
  }

  // Session management
  async createSession(conversationId?: string): Promise<{ session_id: string }> {
    const params = conversationId ? { conversation_id: conversationId } : {};
    return httpClient.post<{ session_id: string }>('/agents/memory/session', params);
  }

  async getUserSessions(limit: number = 10, activeOnly: boolean = true): Promise<{ sessions: UserSession[] }> {
    return httpClient.get<{ sessions: UserSession[] }>('/agents/memory/sessions', {
      params: { limit, active_only: activeOnly }
    });
  }

  async deactivateSession(sessionId: string): Promise<{ message: string }> {
    return httpClient.delete<{ message: string }>(`/agents/memory/session/${sessionId}`);
  }

  // User context
  async getUserContext(): Promise<UserContext> {
    return httpClient.get<UserContext>('/agents/memory/context');
  }

  // Statistics
  async getAgentStats(): Promise<Record<string, any>> {
    return httpClient.get<Record<string, any>>('/agents/stats');
  }

  // Utility methods
  isAgentResponse(response: any): response is AgentChatResponse {
    return response && 
           typeof response.agent_type === 'string' &&
           typeof response.agent_name === 'string' &&
           typeof response.status === 'string' &&
           typeof response.content === 'string';
  }

  getAgentDisplayName(agentType: string): string {
    const displayNames: Record<string, string> = {
      'document_search': 'दस्तावेज खोज',
      'orchestrator': 'मुख्य सहायक',
      'general_chat': 'सामान्य कुराकानी'
    };
    return displayNames[agentType] || agentType;
  }

  getStatusDisplayText(status: string): string {
    const statusTexts: Record<string, string> = {
      'success': 'सफल',
      'error': 'त्रुटि',
      'timeout': 'समय सकियो',
      'partial': 'आंशिक'
    };
    return statusTexts[status] || status;
  }

  getStatusColor(status: string): string {
    const statusColors: Record<string, string> = {
      'success': 'text-green-600',
      'error': 'text-red-600',
      'timeout': 'text-yellow-600',
      'partial': 'text-blue-600'
    };
    return statusColors[status] || 'text-gray-600';
  }

  formatProcessingTime(timeMs?: number): string {
    if (!timeMs) return '';
    
    if (timeMs < 1000) {
      return `${Math.round(timeMs)}ms`;
    } else {
      return `${(timeMs / 1000).toFixed(1)}s`;
    }
  }

  // Error handling
  handleAgentError(error: any): string {
    if (error?.response?.data?.detail) {
      return error.response.data.detail;
    }
    
    if (error?.message) {
      return error.message;
    }
    
    return 'एजेन्ट सिस्टममा समस्या भयो। कृपया फेरि प्रयास गर्नुहोस्।';
  }

  // Configuration
  getDefaultRequest(): Partial<AgentChatRequest> {
    return {
      language: 'nepali',
      max_results: 5,
      include_sources: true
    };
  }
}

// Export singleton instance
export const agentService = new AgentService();
export default agentService;
