// frontend/src/lib/services/chatService.ts

import { httpClient } from './httpClient';

// Types
export interface ChatRequest {
  message: string;
  session_id?: string;
  include_sources?: boolean;
  max_sources?: number;
}

export interface SourceDocument {
  id: string;
  content: string;
  metadata: Record<string, any>;
  score: number;
}

export interface ChatResponse {
  message_id: string;
  user_message: string;
  assistant_message: string;
  sources: SourceDocument[];
  session_id: string;
  timestamp: string;
  processing_time_ms: number;
}

export interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  metadata?: any;
}

export interface SourceNode {
  id: string;
  score: number;
  text: string;
  metadata: Record<string, any>;
}

class ChatService {
  /**
   * Send a message using the new LangChain-based chat service
   */
  async sendMessage(
    message: string,
    sessionId?: string,
    includeSources: boolean = true,
    maxSources: number = 5
  ): Promise<ChatResponse> {
    const request: ChatRequest = {
      message,
      session_id: sessionId,
      include_sources: includeSources,
      max_sources: maxSources
    };

    return httpClient.post<ChatResponse>('/chat/', request);
  }

  /**
   * Simple chat endpoint for quick testing
   */
  async sendSimpleMessage(message: string, sessionId?: string): Promise<{
    user_message: string;
    assistant_message: string;
    sources_count: number;
    session_id: string;
    processing_time_ms: number;
  }> {
    const params = new URLSearchParams({ message });
    if (sessionId) {
      params.append('session_id', sessionId);
    }

    return httpClient.post(`/chat/simple?${params.toString()}`);
  }

  /**
   * Get chat history for a session
   */
  async getChatHistory(sessionId: string, limit: number = 50): Promise<Message[]> {
    const params = new URLSearchParams({ limit: limit.toString() });

    return httpClient.get<Message[]>(`/chat/history/${sessionId}?${params.toString()}`);
  }

  /**
   * List user's chat sessions
   */
  async listSessions(limit: number = 20): Promise<{
    sessions: any[];
    count: number;
  }> {
    const params = new URLSearchParams({ limit: limit.toString() });

    return httpClient.get(`/chat/sessions?${params.toString()}`);
  }

  /**
   * Delete a chat session
   */
  async deleteSession(sessionId: string): Promise<{
    message: string;
    session_id: string;
  }> {
    return httpClient.delete(`/chat/sessions/${sessionId}`);
  }

  /**
   * Get session summary
   */
  async getSessionSummary(sessionId: string): Promise<{
    session_id: string;
    summary: string | null;
    message?: string;
  }> {
    return httpClient.get(`/chat/sessions/${sessionId}/summary`);
  }

  /**
   * Convert API response to Message objects
   */
  convertResponseToMessages(response: ChatResponse): Message[] {
    const userMessage: Message = {
      id: `user_${Date.now()}`,
      content: response.user_message,
      role: 'user',
      timestamp: new Date()
    };

    const aiMessage: Message = {
      id: response.message_id,
      content: response.assistant_message,
      role: 'assistant',
      timestamp: new Date(response.timestamp),
      metadata: {
        sources: response.sources,
        processing_time_ms: response.processing_time_ms,
        session_id: response.session_id
      }
    };

    return [userMessage, aiMessage];
  }

  /**
   * Format timestamp for display
   */
  formatTimestamp(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  }
}

export const chatService = new ChatService();
export default chatService;
