// frontend/src/lib/services/chatService.ts

import { httpClient } from './httpClient';

// Types
export interface ChatRequest {
  message: string;
  thread_id?: string;
}

export interface ChatResponse {
  thread_id: string;
  message_id: string;
  user_message: string;
  ai_response: string;
  sources: any[];
  processing_time_ms: number;
  timestamp: string;
}

export interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  metadata?: any;
}

export interface SourceNode {
  id: string;
  score: number;
  text: string;
  metadata: Record<string, any>;
}

class ChatService {
  /**
   * Send a message to the chat API and get AI response
   * This is a simplified API that handles all chat history in the backend
   */
  async sendMessage(message: string, threadId?: string): Promise<ChatResponse> {
    const params: Record<string, string> = { message };
    if (threadId) {
      params.thread_id = threadId;
    }
    
    return httpClient.post<ChatResponse>('/chat-history/chat', null, { params });
  }

  /**
   * Get source nodes for a specific message
   */
  async getMessageSources(messageId: string): Promise<SourceNode[]> {
    const response = await httpClient.get<{
      message_id: string;
      source_nodes: SourceNode[];
      found: boolean;
      count: number;
    }>(`/chat-history/messages/${messageId}/sources`);
    
    return response.source_nodes || [];
  }

  /**
   * Convert API response to Message objects
   */
  convertResponseToMessages(response: ChatResponse): Message[] {
    const userMessage: Message = {
      id: `user_${Date.now()}`,
      content: response.user_message,
      role: 'user',
      timestamp: new Date()
    };

    const aiMessage: Message = {
      id: response.message_id,
      content: response.ai_response,
      role: 'assistant',
      timestamp: new Date(response.timestamp),
      metadata: {
        sources: response.sources,
        processing_time_ms: response.processing_time_ms
      }
    };

    return [userMessage, aiMessage];
  }

  /**
   * Format timestamp for display
   */
  formatTimestamp(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  }
}

export const chatService = new ChatService();
export default chatService;
