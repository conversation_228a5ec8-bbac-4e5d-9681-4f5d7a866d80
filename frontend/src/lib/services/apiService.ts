class ApiService {
  // Lazy loading to avoid circular import issues
  private get authService() {
    return require('./authService').authService;
  }

  private get knowledgeBaseService() {
    return require('./knowledgeBaseService').knowledgeBaseService;
  }

  private get playgroundService() {
    return require('./playgroundService').playgroundService;
  }

  private get dashboardService() {
    return require('./dashboardService').dashboardService;
  }

  private get agentService() {
    return require('./agentService').agentService;
  }

  // Auth methods
  async login(credentials: { username: string; password: string }) {
    return this.authService.login(credentials);
  }

  async logout() {
    return this.authService.logout();
  }

  async getCurrentUser() {
    return this.authService.getCurrentUser();
  }

  // Knowledge Base methods
  async searchAllDocuments(params: any) {
    return this.knowledgeBaseService.searchAllDocuments(params);
  }

  async uploadDocument(file: File, onProgress?: (progress: number) => void) {
    return this.knowledgeBaseService.uploadDocument(file, onProgress);
  }

  async uploadDocuments(files: File[], onProgress?: (progress: number) => void) {
    return this.knowledgeBaseService.uploadDocuments(files, onProgress);
  }

  async getDocuments() {
    return this.knowledgeBaseService.getDocuments();
  }

  async deleteDocument(documentId: string) {
    return this.knowledgeBaseService.deleteDocument(documentId);
  }

  async getDocument(documentId: string) {
    return this.knowledgeBaseService.getDocument(documentId);
  }

  async retrieveSourceNodes(query: string, maxResults?: number) {
    return this.knowledgeBaseService.retrieveSourceNodes(query, maxResults);
  }

  async queryWithEngine(query: string) {
    return this.knowledgeBaseService.queryWithEngine(query);
  }

  // Playground methods
  async sendMessage(request: any) {
    return this.playgroundService.sendMessage(request);
  }

  async getConversations() {
    return this.playgroundService.getConversations();
  }

  async getConversation(conversationId: string) {
    return this.playgroundService.getConversation(conversationId);
  }

  async deleteConversation(conversationId: string) {
    return this.playgroundService.deleteConversation(conversationId);
  }

  // Dashboard methods
  async getDashboardStats() {
    return this.dashboardService.getDashboardStats();
  }

  async getRecentActivity(limit?: number) {
    return this.dashboardService.getRecentActivity(limit);
  }

  // Agent methods
  async sendToAgent(request: any) {
    return this.agentService.sendToAgent(request);
  }

  async getAgentHealth() {
    return this.agentService.getAgentHealth();
  }

  async listAgents() {
    return this.agentService.listAgents();
  }

  async getAgentInfo(agentName: string) {
    return this.agentService.getAgentInfo(agentName);
  }

  async createAgentSession(conversationId?: string) {
    return this.agentService.createSession(conversationId);
  }

  async getUserSessions(limit?: number, activeOnly?: boolean) {
    return this.agentService.getUserSessions(limit, activeOnly);
  }

  async deactivateAgentSession(sessionId: string) {
    return this.agentService.deactivateSession(sessionId);
  }

  async getUserContext() {
    return this.agentService.getUserContext();
  }

  async getAgentStats() {
    return this.agentService.getAgentStats();
  }
}

// Export singleton instance
export const apiService = new ApiService();
export default apiService;

// Re-export all services for direct access
export { authService } from './authService';
export { knowledgeBaseService } from './knowledgeBaseService';
export { playgroundService } from './playgroundService';
export { dashboardService } from './dashboardService';
export { agentService } from './agentService';
