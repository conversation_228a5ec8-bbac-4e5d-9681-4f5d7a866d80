import { httpClient } from './httpClient';

// Types
interface LoginCredentials {
  username: string;
  password: string;
}

interface LoginResponse {
  id: string;
  access_token: string;
  token_type: string;
  username: string;
  role: string;
  tenant_id: string;
  tenant_label: string;
  tenant_slug: string;
  nav_permission: any;
  token_validity: {
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
    total_seconds: number;
  };
  expires_at: string;
}

interface User {
  id: string;
  username: string;
  role: string;
  tenant_id: string;
  tenant_label: string;
  tenant_slug: string;
}

class AuthService {
  async login(credentials: LoginCredentials): Promise<{ user: User; token: string }> {
    const formData = new FormData();
    formData.append('grant_type', 'password');
    formData.append('username', credentials.username);
    formData.append('password', credentials.password);
    formData.append('scope', '');
    formData.append('client_id', 'legal');

    const response = await httpClient.request<LoginResponse>('/auth/login', {
      method: 'POST',
      body: formData,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      requiresAuth: false
    });

    const user: User = {
      id: response.id,
      username: response.username,
      role: response.role,
      tenant_id: response.tenant_id,
      tenant_label: response.tenant_label,
      tenant_slug: response.tenant_slug
    };

    return {
      user,
      token: response.access_token
    };
  }

  async logout(): Promise<void> {
    await httpClient.post('/auth/logout');
  }

  async getCurrentUser(): Promise<User> {
    return httpClient.get<User>('/auth/me');
  }

  async refreshToken(): Promise<{ token: string }> {
    const response = await httpClient.post<{ access_token: string }>('/auth/refresh');
    return { token: response.access_token };
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    await httpClient.post('/auth/change-password', {
      current_password: currentPassword,
      new_password: newPassword
    });
  }

  async resetPassword(email: string): Promise<void> {
    await httpClient.post('/auth/reset-password', { email }, { requiresAuth: false });
  }

  async confirmResetPassword(token: string, newPassword: string): Promise<void> {
    await httpClient.post('/auth/confirm-reset-password', {
      token,
      new_password: newPassword
    }, { requiresAuth: false });
  }
}

export const authService = new AuthService();
export default authService;
