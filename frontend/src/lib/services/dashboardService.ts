import { httpClient } from './httpClient';

// Types
interface DashboardStats {
  total_documents: number;
  total_conversations: number;
  total_messages: number;
  storage_used: number;
  storage_limit: number;
  recent_activity_count: number;
}

interface ActivityItem {
  id: string;
  type: 'document_upload' | 'conversation_created' | 'message_sent' | 'document_deleted';
  title: string;
  description: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

interface UsageStats {
  date: string;
  documents_uploaded: number;
  messages_sent: number;
  conversations_created: number;
}

interface SystemHealth {
  status: 'healthy' | 'warning' | 'error';
  database_status: 'connected' | 'disconnected';
  vector_db_status: 'connected' | 'disconnected';
  api_response_time: number;
  uptime: number;
}

interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  notifications_enabled: boolean;
  email_notifications: boolean;
  auto_save: boolean;
}

class DashboardService {
  // Stats methods
  async getDashboardStats(): Promise<DashboardStats> {
    return httpClient.get<DashboardStats>('/dashboard/stats');
  }

  async getRecentActivity(limit: number = 10): Promise<ActivityItem[]> {
    return httpClient.get<ActivityItem[]>('/dashboard/activity', { 
      params: { limit } 
    });
  }

  async getUsageStats(days: number = 30): Promise<UsageStats[]> {
    return httpClient.get<UsageStats[]>('/dashboard/usage', { 
      params: { days } 
    });
  }

  async getSystemHealth(): Promise<SystemHealth> {
    return httpClient.get<SystemHealth>('/dashboard/health');
  }

  // User preferences
  async getUserPreferences(): Promise<UserPreferences> {
    return httpClient.get<UserPreferences>('/dashboard/preferences');
  }

  async updateUserPreferences(preferences: Partial<UserPreferences>): Promise<UserPreferences> {
    return httpClient.patch<UserPreferences>('/dashboard/preferences', preferences);
  }

  // Export/Import methods
  async exportData(format: 'json' | 'csv' = 'json'): Promise<Blob> {
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/v1'}/dashboard/export`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${this.getAuthToken()}`,
        'Accept': format === 'json' ? 'application/json' : 'text/csv'
      }
    });

    if (!response.ok) {
      throw new Error(`Export failed: ${response.statusText}`);
    }

    return response.blob();
  }

  async importData(file: File): Promise<{ success: boolean; message: string }> {
    const formData = new FormData();
    formData.append('file', file);

    return httpClient.request<{ success: boolean; message: string }>('/dashboard/import', {
      method: 'POST',
      body: formData,
      headers: {} // Let browser set content-type with boundary
    });
  }

  // Backup methods
  async createBackup(): Promise<{ backup_id: string; message: string }> {
    return httpClient.post<{ backup_id: string; message: string }>('/dashboard/backup');
  }

  async getBackups(): Promise<Array<{ id: string; created_at: string; size: number }>> {
    return httpClient.get<Array<{ id: string; created_at: string; size: number }>>('/dashboard/backups');
  }

  async downloadBackup(backupId: string): Promise<Blob> {
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/v1'}/dashboard/backups/${backupId}/download`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${this.getAuthToken()}`
      }
    });

    if (!response.ok) {
      throw new Error(`Backup download failed: ${response.statusText}`);
    }

    return response.blob();
  }

  async deleteBackup(backupId: string): Promise<void> {
    await httpClient.delete(`/dashboard/backups/${backupId}`);
  }

  private getAuthToken(): string | null {
    try {
      const { store } = require('@/lib/redux/store');
      const state = store.getState();
      return state.auth?.token || null;
    } catch (error) {
      console.warn('Could not get auth token:', error);
      return null;
    }
  }
}

export const dashboardService = new DashboardService();
export default dashboardService;
