interface RequestOptions extends RequestInit {
  params?: Record<string, string | number | boolean | undefined>;
  requiresAuth?: boolean;
}

class HttpClient {
  private baseURL: string;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  private getAuthHeaders(): HeadersInit {
    // Get token from localStorage to avoid circular dependency
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('authToken');
      return token ? { 'Authorization': `Bearer ${token}` } : {};
    }
    return {};
  }

  private buildUrl(endpoint: string, params?: Record<string, string | number | boolean | undefined>): string {
    const url = new URL(`${this.baseURL}${endpoint}`);
    
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          url.searchParams.append(key, String(value));
        }
      });
    }
    
    return url.toString();
  }

  async request<T>(endpoint: string, options: RequestOptions = {}): Promise<T> {
    const { params, requiresAuth = true, ...fetchOptions } = options;

    const url = this.buildUrl(endpoint, params);

    // Don't set default Content-Type if it's already set in options
    const hasContentType = fetchOptions.headers &&
      Object.keys(fetchOptions.headers).some(h => h.toLowerCase() === 'content-type');

    const headers = new Headers({
      ...(!hasContentType && { 'Content-Type': 'application/json' }),
      ...fetchOptions.headers,
      ...(requiresAuth ? this.getAuthHeaders() : {})
    });

    // Convert FormData to URLSearchParams if Content-Type is application/x-www-form-urlencoded
    let body = fetchOptions.body;
    if (body instanceof FormData &&
        headers.get('Content-Type') === 'application/x-www-form-urlencoded') {
      const urlSearchParams = new URLSearchParams();
      body.forEach((value, key) => {
        urlSearchParams.append(key, value.toString());
      });
      body = urlSearchParams;
    }

    const response = await fetch(url, {
      ...fetchOptions,
      body,
      headers
    });

    // Handle 401 Unauthorized - could trigger logout or token refresh
    if (response.status === 401) {
      console.error('Authentication error: Unauthorized');
      // Could dispatch a logout action here
      // store.dispatch(logout());
    }

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `Request failed with status ${response.status}`);
    }

    // For 204 No Content
    if (response.status === 204) {
      return {} as T;
    }

    return await response.json();
  }

  async get<T>(endpoint: string, options: RequestOptions = {}): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'GET' });
  }

  async post<T>(endpoint: string, data?: any, options: RequestOptions = {}): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined
    });
  }

  async put<T>(endpoint: string, data?: any, options: RequestOptions = {}): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined
    });
  }

  async patch<T>(endpoint: string, data?: any, options: RequestOptions = {}): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined
    });
  }

  async delete<T>(endpoint: string, options: RequestOptions = {}): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'DELETE' });
  }

  // Method for file uploads
  async uploadFile<T>(endpoint: string, file: File, options: RequestOptions = {}): Promise<T> {
    const formData = new FormData();
    formData.append('file', file);
    
    return this.request<T>(endpoint, {
      ...options,
      method: 'POST',
      body: formData,
      headers: {} // Let the browser set the content type with boundary
    });
  }

  // Method for multiple file uploads
  async uploadFiles<T>(endpoint: string, files: File[], options: RequestOptions = {}): Promise<T> {
    const formData = new FormData();
    files.forEach((file, index) => {
      formData.append(`file${index}`, file);
    });
    
    return this.request<T>(endpoint, {
      ...options,
      method: 'POST',
      body: formData,
      headers: {} // Let the browser set the content type with boundary
    });
  }
}

// Create and export a singleton instance
export const httpClient = new HttpClient(process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/v1');

export default HttpClient;
