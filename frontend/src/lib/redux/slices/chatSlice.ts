import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { apiService } from '@/lib/services/apiService';

interface Source {
  documentId: string;
  filename: string;
  pageNumber: number;
  snippet: string;
  confidenceScore: number;
}

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  sources?: Source[];
  conversationId?: string;
}

interface Conversation {
  id: string;
  title: string;
  createdAt: Date;
  updatedAt: Date;
  messageCount: number;
}

interface ChatState {
  conversations: Conversation[];
  currentConversation: string | null;
  messages: Message[];
  isLoading: boolean;
  isTyping: boolean;
  error: string | null;
  searchResults: any[];
}

const initialState: ChatState = {
  conversations: [],
  currentConversation: null,
  messages: [],
  isLoading: false,
  isTyping: false,
  error: null,
  searchResults: [],
};

// Async thunks
export const sendMessage = createAsyncThunk(
  'chat/sendMessage',
  async (
    params: { 
      message: string; 
      conversationId?: string; 
      maxResults?: number 
    }, 
    { rejectWithValue }
  ) => {
    try {
      const response = await apiService.sendChatMessage(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to send message');
    }
  }
);

export const fetchConversations = createAsyncThunk(
  'chat/fetchConversations',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiService.getChatConversations();
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch conversations');
    }
  }
);

export const fetchChatHistory = createAsyncThunk(
  'chat/fetchChatHistory',
  async (conversationId: string, { rejectWithValue }) => {
    try {
      const response = await apiService.getChatHistory(conversationId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch chat history');
    }
  }
);

export const deleteConversation = createAsyncThunk(
  'chat/deleteConversation',
  async (conversationId: string, { rejectWithValue }) => {
    try {
      await apiService.deleteConversation(conversationId);
      return conversationId;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to delete conversation');
    }
  }
);

export const searchInChat = createAsyncThunk(
  'chat/searchInChat',
  async (query: string, { rejectWithValue }) => {
    try {
      const response = await apiService.searchInChat(query);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Search failed');
    }
  }
);

const chatSlice = createSlice({
  name: 'chat',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentConversation: (state, action: PayloadAction<string | null>) => {
      state.currentConversation = action.payload;
    },
    addUserMessage: (state, action: PayloadAction<Message>) => {
      state.messages.push(action.payload);
    },
    setTyping: (state, action: PayloadAction<boolean>) => {
      state.isTyping = action.payload;
    },
    clearMessages: (state) => {
      state.messages = [];
      state.currentConversation = null;
    },
    clearSearchResults: (state) => {
      state.searchResults = [];
    },
    updateMessage: (state, action: PayloadAction<{ id: string; updates: Partial<Message> }>) => {
      const messageIndex = state.messages.findIndex(msg => msg.id === action.payload.id);
      if (messageIndex !== -1) {
        state.messages[messageIndex] = { ...state.messages[messageIndex], ...action.payload.updates };
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Send message
      .addCase(sendMessage.pending, (state) => {
        state.isTyping = true;
        state.error = null;
      })
      .addCase(sendMessage.fulfilled, (state, action) => {
        state.isTyping = false;
        state.messages.push({
          id: Date.now().toString(),
          type: 'assistant',
          content: action.payload.answer,
          timestamp: new Date(),
          sources: action.payload.sources,
          conversationId: action.payload.conversationId,
        });
        state.currentConversation = action.payload.conversationId;
        state.error = null;
      })
      .addCase(sendMessage.rejected, (state, action) => {
        state.isTyping = false;
        state.error = action.payload as string;
      })
      // Fetch conversations
      .addCase(fetchConversations.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(fetchConversations.fulfilled, (state, action) => {
        state.isLoading = false;
        state.conversations = action.payload;
      })
      .addCase(fetchConversations.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Fetch chat history
      .addCase(fetchChatHistory.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(fetchChatHistory.fulfilled, (state, action) => {
        state.isLoading = false;
        state.messages = action.payload.map((chat: any) => [
          {
            id: `${chat.id}-user`,
            type: 'user' as const,
            content: chat.query,
            timestamp: new Date(chat.timestamp),
            conversationId: chat.conversationId,
          },
          {
            id: `${chat.id}-assistant`,
            type: 'assistant' as const,
            content: chat.response,
            timestamp: new Date(chat.timestamp),
            sources: chat.sources,
            conversationId: chat.conversationId,
          },
        ]).flat();
      })
      .addCase(fetchChatHistory.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Delete conversation
      .addCase(deleteConversation.fulfilled, (state, action) => {
        state.conversations = state.conversations.filter(conv => conv.id !== action.payload);
        if (state.currentConversation === action.payload) {
          state.currentConversation = null;
          state.messages = [];
        }
      })
      // Search in chat
      .addCase(searchInChat.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(searchInChat.fulfilled, (state, action) => {
        state.isLoading = false;
        state.searchResults = action.payload;
      })
      .addCase(searchInChat.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  clearError,
  setCurrentConversation,
  addUserMessage,
  setTyping,
  clearMessages,
  clearSearchResults,
  updateMessage,
} = chatSlice.actions;

export default chatSlice.reducer;
