import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { chatHistoryService, ChatThread, MessageResponse, ChatHistoryStats, CreateThreadRequest, SendMessageRequest } from '@/lib/services/chatHistoryService';

interface ChatHistoryState {
  // Threads
  threads: ChatThread[];
  currentThread: ChatThread | null;
  threadsLoading: boolean;
  threadsError: string | null;

  // Messages
  messages: MessageResponse[];
  messagesLoading: boolean;
  messagesError: string | null;

  // Source nodes mapping (messageId -> source nodes)
  sourceNodesMap: Record<string, any[]>;
  sourceNodesLoading: Record<string, boolean>;

  // UI state
  isTyping: boolean;
  selectedThreadIds: string[];
  selectedMessageId: string | null; // For displaying source nodes

  // Pagination
  threadsPagination: {
    total: number;
    page: number;
    hasMore: boolean;
  };

  messagesPagination: {
    total: number;
    hasMore: boolean;
  };

  // Statistics
  stats: ChatHistoryStats | null;
  statsLoading: boolean;
}

const initialState: ChatHistoryState = {
  threads: [],
  currentThread: null,
  threadsLoading: false,
  threadsError: null,

  messages: [],
  messagesLoading: false,
  messagesError: null,

  sourceNodesMap: {},
  sourceNodesLoading: {},

  isTyping: false,
  selectedThreadIds: [],
  selectedMessageId: null,

  threadsPagination: {
    total: 0,
    page: 1,
    hasMore: false,
  },

  messagesPagination: {
    total: 0,
    hasMore: false,
  },

  stats: null,
  statsLoading: false,
};

// Async thunks
export const createThread = createAsyncThunk(
  'chatHistory/createThread',
  async (request: CreateThreadRequest, { rejectWithValue }) => {
    try {
      return await chatHistoryService.createThread(request);
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to create thread');
    }
  }
);

export const fetchThreads = createAsyncThunk(
  'chatHistory/fetchThreads',
  async (params: { limit?: number; offset?: number; status?: string } = {}, { rejectWithValue }) => {
    try {
      return await chatHistoryService.listThreads(params);
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch threads');
    }
  }
);

export const fetchThread = createAsyncThunk(
  'chatHistory/fetchThread',
  async (threadId: string, { rejectWithValue }) => {
    try {
      return await chatHistoryService.getThread(threadId);
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch thread');
    }
  }
);

export const sendMessage = createAsyncThunk(
  'chatHistory/sendMessage',
  async (request: SendMessageRequest, { rejectWithValue }) => {
    try {
      return await chatHistoryService.sendMessage(request);
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to send message');
    }
  }
);

export const fetchMessages = createAsyncThunk(
  'chatHistory/fetchMessages',
  async (params: { threadId: string; limit?: number; offset?: number }, { rejectWithValue }) => {
    try {
      return await chatHistoryService.getMessages(params.threadId, {
        limit: params.limit,
        offset: params.offset,
      });
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch messages');
    }
  }
);

export const deleteThread = createAsyncThunk(
  'chatHistory/deleteThread',
  async (params: { threadId: string; permanent?: boolean }, { rejectWithValue }) => {
    try {
      await chatHistoryService.deleteThread(params.threadId, params.permanent);
      return params.threadId;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to delete thread');
    }
  }
);

export const fetchStats = createAsyncThunk(
  'chatHistory/fetchStats',
  async (_, { rejectWithValue }) => {
    try {
      return await chatHistoryService.getChatStats();
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch stats');
    }
  }
);

export const fetchMessageSources = createAsyncThunk(
  'chatHistory/fetchMessageSources',
  async (messageId: string, { rejectWithValue }) => {
    try {
      const result = await chatHistoryService.getMessageSources(messageId);
      return {
        messageId,
        sourceNodes: result.source_nodes || []
      };
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch message sources');
    }
  }
);

const chatHistorySlice = createSlice({
  name: 'chatHistory',
  initialState,
  reducers: {
    setCurrentThread: (state, action: PayloadAction<ChatThread | null>) => {
      state.currentThread = action.payload;
      // Clear messages when switching threads
      if (action.payload?.thread_id !== state.currentThread?.thread_id) {
        state.messages = [];
        state.messagesPagination = { total: 0, hasMore: false };
      }
    },
    
    clearMessages: (state) => {
      state.messages = [];
      state.messagesPagination = { total: 0, hasMore: false };
    },
    
    setTyping: (state, action: PayloadAction<boolean>) => {
      state.isTyping = action.payload;
    },
    
    toggleThreadSelection: (state, action: PayloadAction<string>) => {
      const threadId = action.payload;
      const index = state.selectedThreadIds.indexOf(threadId);
      if (index > -1) {
        state.selectedThreadIds.splice(index, 1);
      } else {
        state.selectedThreadIds.push(threadId);
      }
    },
    
    clearThreadSelection: (state) => {
      state.selectedThreadIds = [];
    },
    
    selectAllThreads: (state) => {
      state.selectedThreadIds = state.threads.map(thread => thread.thread_id);
    },
    
    clearErrors: (state) => {
      state.threadsError = null;
      state.messagesError = null;
    },

    updateThreadInList: (state, action: PayloadAction<ChatThread>) => {
      const index = state.threads.findIndex(t => t.thread_id === action.payload.thread_id);
      if (index > -1) {
        state.threads[index] = action.payload;
      }
      if (state.currentThread?.thread_id === action.payload.thread_id) {
        state.currentThread = action.payload;
      }
    },

    removeThreadFromList: (state, action: PayloadAction<string>) => {
      state.threads = state.threads.filter(t => t.thread_id !== action.payload);
      if (state.currentThread?.thread_id === action.payload) {
        state.currentThread = null;
        state.messages = [];
      }
    },

    setSelectedMessageId: (state, action: PayloadAction<string | null>) => {
      state.selectedMessageId = action.payload;
    },

    addSourceNodes: (state, action: PayloadAction<{ messageId: string, sourceNodes: any[] }>) => {
      const { messageId, sourceNodes } = action.payload;
      state.sourceNodesMap[messageId] = sourceNodes;
      state.sourceNodesLoading[messageId] = false;
    },

    setSourceNodesLoading: (state, action: PayloadAction<{ messageId: string, loading: boolean }>) => {
      const { messageId, loading } = action.payload;
      state.sourceNodesLoading[messageId] = loading;
    },

    clearSourceNodes: (state) => {
      state.sourceNodesMap = {};
      state.sourceNodesLoading = {};
    },
  },
  
  extraReducers: (builder) => {
    builder
      // Create thread
      .addCase(createThread.pending, (state) => {
        state.threadsLoading = true;
        state.threadsError = null;
      })
      .addCase(createThread.fulfilled, (state, action) => {
        state.threadsLoading = false;
        state.threads.unshift(action.payload);
        state.currentThread = action.payload;
      })
      .addCase(createThread.rejected, (state, action) => {
        state.threadsLoading = false;
        state.threadsError = action.payload as string;
      })
      
      // Fetch threads
      .addCase(fetchThreads.pending, (state) => {
        state.threadsLoading = true;
        state.threadsError = null;
      })
      .addCase(fetchThreads.fulfilled, (state, action) => {
        state.threadsLoading = false;
        state.threads = action.payload.threads;
        state.threadsPagination = {
          total: action.payload.total_count,
          page: action.payload.page,
          hasMore: action.payload.has_more,
        };
      })
      .addCase(fetchThreads.rejected, (state, action) => {
        state.threadsLoading = false;
        state.threadsError = action.payload as string;
      })
      
      // Fetch thread
      .addCase(fetchThread.fulfilled, (state, action) => {
        state.currentThread = action.payload;
        // Update in threads list if present
        const index = state.threads.findIndex(t => t.thread_id === action.payload.thread_id);
        if (index > -1) {
          state.threads[index] = action.payload;
        }
      })
      
      // Send message
      .addCase(sendMessage.pending, (state) => {
        state.isTyping = true;
        state.messagesError = null;
      })
      .addCase(sendMessage.fulfilled, (state, action) => {
        state.isTyping = false;
        // Add user message and AI response to messages
        const userMessage: MessageResponse = {
          id: `user_${Date.now()}`,
          content: action.payload.user_message,
          type: 'human',
          timestamp: action.payload.timestamp,
          additional_kwargs: {},
        };
        const aiMessage: MessageResponse = {
          id: action.payload.message_id,
          content: action.payload.ai_response,
          type: 'ai',
          timestamp: action.payload.timestamp,
          additional_kwargs: { sources: action.payload.sources },
        };
        state.messages.push(userMessage, aiMessage);
        
        // Update current thread message count
        if (state.currentThread) {
          state.currentThread.message_count += 2;
          state.currentThread.user_message_count += 1;
          state.currentThread.ai_message_count += 1;
          state.currentThread.last_activity = action.payload.timestamp;
        }
      })
      .addCase(sendMessage.rejected, (state, action) => {
        state.isTyping = false;
        state.messagesError = action.payload as string;
      })
      
      // Fetch messages
      .addCase(fetchMessages.pending, (state) => {
        state.messagesLoading = true;
        state.messagesError = null;
      })
      .addCase(fetchMessages.fulfilled, (state, action) => {
        state.messagesLoading = false;
        state.messages = action.payload.messages;
        state.messagesPagination = {
          total: action.payload.total_count,
          hasMore: action.payload.has_more,
        };
      })
      .addCase(fetchMessages.rejected, (state, action) => {
        state.messagesLoading = false;
        state.messagesError = action.payload as string;
      })
      
      // Delete thread
      .addCase(deleteThread.fulfilled, (state, action) => {
        state.threads = state.threads.filter(t => t.thread_id !== action.payload);
        if (state.currentThread?.thread_id === action.payload) {
          state.currentThread = null;
          state.messages = [];
        }
        state.selectedThreadIds = state.selectedThreadIds.filter(id => id !== action.payload);
      })
      
      // Fetch stats
      .addCase(fetchStats.pending, (state) => {
        state.statsLoading = true;
      })
      .addCase(fetchStats.fulfilled, (state, action) => {
        state.statsLoading = false;
        state.stats = action.payload;
      })
      .addCase(fetchStats.rejected, (state) => {
        state.statsLoading = false;
      })

      // Fetch message sources
      .addCase(fetchMessageSources.pending, (state, action) => {
        const messageId = action.meta.arg;
        state.sourceNodesLoading[messageId] = true;
      })
      .addCase(fetchMessageSources.fulfilled, (state, action) => {
        const { messageId, sourceNodes } = action.payload;
        state.sourceNodesMap[messageId] = sourceNodes;
        state.sourceNodesLoading[messageId] = false;
      })
      .addCase(fetchMessageSources.rejected, (state, action) => {
        const messageId = action.meta.arg;
        state.sourceNodesLoading[messageId] = false;
      });
  },
});

export const {
  setCurrentThread,
  clearMessages,
  setTyping,
  toggleThreadSelection,
  clearThreadSelection,
  selectAllThreads,
  clearErrors,
  updateThreadInList,
  removeThreadFromList,
  setSelectedMessageId,
  addSourceNodes,
  setSourceNodesLoading,
  clearSourceNodes,
} = chatHistorySlice.actions;

export default chatHistorySlice.reducer;
