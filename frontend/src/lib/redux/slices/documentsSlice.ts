import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { apiService } from '@/lib/services/apiService';

interface Document {
  id: string;
  title: string;
  filename: string;
  type: string;
  size: string;
  uploadedAt: string;
  uploadedBy: string;
  pages: number;
  status: 'processing' | 'processed' | 'error';
  presignedUrl?: string;
}

interface DocumentsState {
  documents: Document[];
  selectedDocument: Document | null;
  isLoading: boolean;
  isUploading: boolean;
  uploadProgress: number;
  error: string | null;
  searchQuery: string;
  filters: {
    type: string;
    status: string;
  };
  pagination: {
    page: number;
    limit: number;
    total: number;
  };
}

const initialState: DocumentsState = {
  documents: [],
  selectedDocument: null,
  isLoading: false,
  isUploading: false,
  uploadProgress: 0,
  error: null,
  searchQuery: '',
  filters: {
    type: 'all',
    status: 'all',
  },
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
  },
};

// Async thunks
export const fetchDocuments = createAsyncThunk(
  'documents/fetchDocuments',
  async (params: { page?: number; limit?: number; search?: string }, { rejectWithValue }) => {
    try {
      const response = await apiService.getDocuments(params);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch documents');
    }
  }
);

export const uploadDocuments = createAsyncThunk(
  'documents/uploadDocuments',
  async (files: File[], { rejectWithValue, dispatch }) => {
    try {
      const response = await apiService.uploadDocuments(files, (progress) => {
        dispatch(setUploadProgress(progress));
      });
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Upload failed');
    }
  }
);

export const deleteDocument = createAsyncThunk(
  'documents/deleteDocument',
  async (documentId: string, { rejectWithValue }) => {
    try {
      await apiService.deleteDocument(documentId);
      return documentId;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to delete document');
    }
  }
);

export const getDocumentDetails = createAsyncThunk(
  'documents/getDocumentDetails',
  async (documentId: string, { rejectWithValue }) => {
    try {
      const response = await apiService.getDocument(documentId);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to get document details');
    }
  }
);

const documentsSlice = createSlice({
  name: 'documents',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.searchQuery = action.payload;
    },
    setFilters: (state, action: PayloadAction<Partial<typeof initialState.filters>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setUploadProgress: (state, action: PayloadAction<number>) => {
      state.uploadProgress = action.payload;
    },
    resetUpload: (state) => {
      state.isUploading = false;
      state.uploadProgress = 0;
    },
    setSelectedDocument: (state, action: PayloadAction<Document | null>) => {
      state.selectedDocument = action.payload;
    },
    setPagination: (state, action: PayloadAction<Partial<typeof initialState.pagination>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch documents
      .addCase(fetchDocuments.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchDocuments.fulfilled, (state, action) => {
        state.isLoading = false;
        state.documents = action.payload.documents;
        state.pagination.total = action.payload.total;
        state.error = null;
      })
      .addCase(fetchDocuments.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Upload documents
      .addCase(uploadDocuments.pending, (state) => {
        state.isUploading = true;
        state.uploadProgress = 0;
        state.error = null;
      })
      .addCase(uploadDocuments.fulfilled, (state, action) => {
        state.isUploading = false;
        state.uploadProgress = 100;
        // Add new documents to the list
        state.documents = [...action.payload.documents, ...state.documents];
        state.error = null;
      })
      .addCase(uploadDocuments.rejected, (state, action) => {
        state.isUploading = false;
        state.uploadProgress = 0;
        state.error = action.payload as string;
      })
      // Delete document
      .addCase(deleteDocument.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(deleteDocument.fulfilled, (state, action) => {
        state.isLoading = false;
        state.documents = state.documents.filter(doc => doc.id !== action.payload);
        if (state.selectedDocument?.id === action.payload) {
          state.selectedDocument = null;
        }
      })
      .addCase(deleteDocument.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      // Get document details
      .addCase(getDocumentDetails.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(getDocumentDetails.fulfilled, (state, action) => {
        state.isLoading = false;
        state.selectedDocument = action.payload;
      })
      .addCase(getDocumentDetails.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  clearError,
  setSearchQuery,
  setFilters,
  setUploadProgress,
  resetUpload,
  setSelectedDocument,
  setPagination,
} = documentsSlice.actions;

export default documentsSlice.reducer;
