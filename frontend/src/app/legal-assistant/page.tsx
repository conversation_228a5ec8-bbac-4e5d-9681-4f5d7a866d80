'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import ProtectedLayout from '@/components/layout/ProtectedLayout';
import ChatPlayground, {
  type PlaygroundMode,
  type Message,
  type RetrieveResponse,
  type AgentResponse
} from '@/components/ChatPlayground';
import DocumentsBrowser, {
  type SearchResult
} from '@/components/DocumentsBrowser';
import { apiService } from '@/lib/services/apiService';
import { chatService } from '@/lib/services/chatService';

interface SearchResponse {
  response: SearchResult[];
  total_count: number;
  page: number;
  page_size: number;
  total_pages: number;
  next_offset?: string;
  has_more: boolean;
  query?: string;
  processing_time_ms: number;
}

const LegalAssistantPage = () => {

  // Local state management
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      content: 'नमस्कार! म तपाईंको कानुनी सहायक हुँ। म तपाईंलाई कानुनी दस्तावेजहरू खोज्न र कानुनी प्रश्नहरूको जवाफ दिन मद्दत गर्न सक्छु। तपाईं के खोज्दै हुनुहुन्छ?',
      role: 'assistant',
      timestamp: new Date()
    }
  ]);
  const [inputMessage, setInputMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [hasMore, setHasMore] = useState(false);
  const [nextOffset, setNextOffset] = useState<string | undefined>();
  const [playgroundMode, setPlaygroundMode] = useState<PlaygroundMode>('agent'); // Default to agent mode
  const [sourceNodes, setSourceNodes] = useState<RetrieveResponse[]>([]);
  const [selectedMessageId, setSelectedMessageId] = useState<string | null>(null);
  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);

  // Load initial documents and chat history
  useEffect(() => {
    loadDocuments();
    loadChatHistory();
  }, []);

  const loadChatHistory = async () => {
    try {
      // Only load if we have a session ID
      if (currentSessionId) {
        const history = await chatService.getChatHistory(currentSessionId, 20);
        if (history.length > 0) {
          // Convert API messages to our Message format
          const convertedMessages = history.reverse().map(msg => ({
            id: msg.id,
            content: msg.content,
            role: msg.role as 'user' | 'assistant',
            timestamp: new Date(msg.timestamp)
          }));

          setMessages(convertedMessages);
        }
      }
    } catch (error) {
      console.error('Error loading chat history:', error);
    }
  };

  const loadDocuments = async (append: boolean = false) => {
    setIsSearching(true);
    try {
      const response: SearchResponse = await apiService.searchAllDocuments({
        limit: 12,
        offset: append ? nextOffset : undefined
      });

      if (append) {
        setSearchResults(prev => [...prev, ...response.response]);
      } else {
        setSearchResults(response.response);
      }

      setHasMore(response.has_more);
      setNextOffset(response.next_offset);
    } catch (error) {
      console.error('Error loading documents:', error);
    } finally {
      setIsSearching(false);
    }
  };

  const loadMoreDocuments = () => {
    if (hasMore && !isSearching) {
      loadDocuments(true);
    }
  };

  // Handle message selection and source nodes fetching
  const handleMessageSelect = async (messageId: string, messageType: 'user' | 'assistant') => {
    if (messageType === 'assistant') {
      // Set selected message
      setSelectedMessageId(messageId);

      // For now, we'll get sources from the message metadata
      // In the new system, sources are included in the response
      const message = messages.find(m => m.id === messageId);
      if (message && (message as any).metadata && (message as any).metadata.sources) {
        // Convert sources to the expected format
        const convertedSources = (message as any).metadata.sources.map((source: any) => ({
          id: source.id,
          text: source.content,
          metadata: source.metadata,
          score: source.score
        }));
        setSourceNodes(convertedSources);
      }
    }
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;

    const currentInput = inputMessage;
    setInputMessage('');
    setIsLoading(true);

    // Add user message immediately for UI responsiveness
    const userMessage: Message = {
      id: Date.now().toString(),
      content: currentInput,
      role: 'user',
      timestamp: new Date()
    };
    setMessages(prev => [...prev, userMessage]);

    try {
      // Use the new LangChain-based chat service
      const response = await chatService.sendMessage(currentInput, currentSessionId || undefined);

      // Update session ID if we got a new one
      if (!currentSessionId) {
        setCurrentSessionId(response.session_id);
      }

      // Add AI response to chat
      const aiMessage: Message = {
        id: response.message_id,
        content: response.assistant_message,
        role: 'assistant',
        timestamp: new Date(response.timestamp)
      };

      // Add metadata with sources
      (aiMessage as any).metadata = {
        sources: response.sources,
        processing_time_ms: response.processing_time_ms,
        session_id: response.session_id
      };

      setMessages(prev => [...prev, aiMessage]);

      // Set source nodes from the response
      if (response.sources && response.sources.length > 0) {
        // Convert sources to the expected format
        const convertedSources = response.sources.map(source => ({
          id: source.id,
          text: source.content,
          metadata: source.metadata,
          score: source.score
        }));
        setSourceNodes(convertedSources);
      }

    } catch (error) {
      console.error('Error sending message:', error);

      // Add error message to chat
      const errorMessage: Message = {
        id: Date.now().toString(),
        content: 'माफ गर्नुहोस्, तपाईंको सन्देश पठाउन समस्या भयो। कृपया फेरि प्रयास गर्नुहोस्।',
        role: 'assistant',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  // Component event handlers
  const handleInputChange = (value: string) => {
    setInputMessage(value);
  };

  const handleModeChange = (mode: PlaygroundMode) => {
    setPlaygroundMode(mode);
  };

  const handleSourceNodesUpdate = (nodes: RetrieveResponse[]) => {
    setSourceNodes(nodes);
  };

  const handleQueryResponseUpdate = (_response: string) => {
    // Not needed in simplified approach
  };

  const handleAgentResponseUpdate = (_response: AgentResponse) => {
    // Not needed in simplified approach
  };

  return (
    <ProtectedLayout>
      <div className="h-full flex gap-6 p-6 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 overflow-hidden">
        {/* Chat Playground Component - 50% width, 90% height */}
        <div className="w-1/2 h-[90vh] flex flex-col bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden backdrop-blur-sm">
          <ChatPlayground
            messages={messages}
            inputMessage={inputMessage}
            isLoading={isLoading}
            playgroundMode={playgroundMode}
            onSendMessage={handleSendMessage}
            onInputChange={handleInputChange}
            onModeChange={handleModeChange}
            onSourceNodesUpdate={handleSourceNodesUpdate}
            onQueryResponseUpdate={handleQueryResponseUpdate}
            onAgentResponseUpdate={handleAgentResponseUpdate}
            onMessageSelect={handleMessageSelect}
            selectedMessageId={selectedMessageId}
          />
        </div>

        {/* Documents Browser Component - 50% width, 90% height */}
        <div className="w-1/2 h-[90vh] flex flex-col bg-white dark:bg-gray-800 rounded-2xl shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden backdrop-blur-sm">
          <DocumentsBrowser
            searchResults={searchResults}
            sourceNodes={sourceNodes}
            searchQuery=""
            isSearching={isSearching}
            hasMore={hasMore}
            onLoadMore={loadMoreDocuments}
          />
        </div>
      </div>
    </ProtectedLayout>
  );
};

export default LegalAssistantPage;
