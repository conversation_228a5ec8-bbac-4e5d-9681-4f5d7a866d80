{"cells": [{"cell_type": "code", "execution_count": null, "id": "ce8393bb", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["usage: ipykernel_launcher.py [-h] inputFile outputFile\n", "ipykernel_launcher.py: error: the following arguments are required: inputFile, outputFile\n"]}, {"ename": "SystemExit", "evalue": "2", "output_type": "error", "traceback": ["An exception has occurred, use %tb to see the full traceback.\n", "\u001b[31mSystemExit\u001b[39m\u001b[31m:\u001b[39m 2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/IPython/core/interactiveshell.py:3707: UserWarning: To exit: use 'exit', 'quit', or Ctrl-D.\n", "  warn(\"To exit: use 'exit', 'quit', or Ctrl-D.\", stacklevel=1)\n"]}], "source": []}, {"cell_type": "code", "execution_count": null, "id": "619fd9fb", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "legal", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}