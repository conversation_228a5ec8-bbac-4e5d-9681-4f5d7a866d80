# LangChain Dependencies for New Chat Service
# Install with: pip install -r requirements_langchain.txt

# Core LangChain
langchain==0.1.0
langchain-core==0.1.0
langchain-community==0.0.13

# LangChain OpenAI Integration
langchain-openai==0.0.5

# LangChain MongoDB Integration
langchain-mongodb==0.1.0

# Additional dependencies for memory and checkpoints
pymongo>=4.6.0
motor>=3.3.0

# For LSTM and advanced memory (if needed)
torch>=2.0.0
transformers>=4.30.0

# For async operations
asyncio
aiofiles

# For better logging and monitoring
structlog
