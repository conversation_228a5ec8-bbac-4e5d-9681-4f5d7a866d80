from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import RedirectResponse
from contextlib import asynccontextmanager

from app.v1.router import v1_router
from app.shared.database import init_tenant_admin_db, create_default_tenant, create_default_tenant_admin
from app.shared.utils.logger import setup_new_logging

# Initialize logger
logger = setup_new_logging(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Lifespan context manager for the FastAPI application.
    """
    # Startup: Initialize tenant admin database
    logger.info("Initializing tenant admin database...")
    init_tenant_admin_db()

    # Create default tenant and admin user (only for development)
    try:
        tenant_id = create_default_tenant()
        create_default_tenant_admin(tenant_id)
        logger.info("Default tenant and admin user created successfully")
    except Exception as e:
        logger.warning(f"Could not create default tenant/admin: {e}")

    logger.info("Application startup completed")

    # Yield control back to FastAPI
    yield

    # Shutdown
    logger.info("Application shutting down")

app = FastAPI(
    title="Legal Document Processing API",
    description="Legal document processing API with Argon2 authentication",
    version="0.1.0",
    lifespan=lifespan
)

origins = ["*"]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(v1_router)

@app.get("/")
async def root():
    return RedirectResponse(url="/docs")

@app.get("/health")
async def health_check():
    """Health check endpoint for load balancer and monitoring."""
    return {
        "status": "healthy",
        "service": "legal-api",
        "version": "0.1.0"
    }