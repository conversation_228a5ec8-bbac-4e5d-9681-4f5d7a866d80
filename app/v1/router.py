# app/v1/router.py

from fastapi import APIRouter

from .services.auth.routes import router as auth_router
from .services.knowledge_base.routes import router as knowledge_base_router
from .services.chat.routes import router as chat_router
from .services.chat_history.routes import router as chat_history_router
from .services.tenant.routes import router as tenant_router
from .services.agents.routes import router as agents_router

# Create the main v1 router
v1_router = APIRouter(prefix="/v1")

# Include all service routers
v1_router.include_router(auth_router)
v1_router.include_router(knowledge_base_router)
v1_router.include_router(chat_router)
v1_router.include_router(chat_history_router)
v1_router.include_router(tenant_router)
v1_router.include_router(agents_router)
