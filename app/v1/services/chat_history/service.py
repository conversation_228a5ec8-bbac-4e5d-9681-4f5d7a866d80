# app/v1/services/chat_history/service.py

import time
import uuid
from typing import List, Dict, Optional, Any
from datetime import datetime, timedelta
from fastapi import HTTPException
from pymongo import MongoClient
from pymongo.collection import Collection

# LangChain imports
from langchain_mongodb import MongoDBChatMessageHistory
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage

from app.shared.database.models import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from app.v1.services.knowledge_base.service import KnowledgeBaseService
from .models import (
    ChatThread, AIResponseRecord, CreateThreadRequest, UpdateThreadRequest,
    SendMessageRequest, GetMessagesRequest, ThreadListResponse, MessageListResponse,
    ChatResponse, MessageResponse, ChatHistoryStats, ThreadStatus
)

logger = setup_new_logging(__name__)

class ChatHistoryService:
    """
    Chat history service that integrates Lang<PERSON>hain MongoDB chat message history
    with custom thread management and analytics.
    """
    
    def __init__(self, current_user: UserTenantDB):
        self.current_user = current_user
        self.db = current_user.adb
        
        # Collections
        self.threads_collection: Collection = self.db.chat_threads
        self.ai_responses_collection: Collection = self.db.ai_responses
        
        # Create indexes
        self._create_indexes()
    
    def _create_indexes(self):
        """Create necessary indexes for efficient queries."""
        try:
            # Thread indexes
            self.threads_collection.create_index([("user_id", 1), ("created_at", -1)])
            self.threads_collection.create_index([("user_id", 1), ("status", 1)])
            self.threads_collection.create_index([("user_id", 1), ("last_activity", -1)])
            self.threads_collection.create_index([("langchain_session_id", 1)])
            
            # AI response indexes
            self.ai_responses_collection.create_index([("user_id", 1), ("timestamp", -1)])
            self.ai_responses_collection.create_index([("thread_id", 1), ("timestamp", -1)])
            self.ai_responses_collection.create_index([("langchain_session_id", 1)])
            
            logger.debug("Created chat history indexes")
        except Exception as e:
            logger.warning(f"Error creating indexes: {e}")
    
    def _get_langchain_history(self, session_id: str) -> MongoDBChatMessageHistory:
        """Get LangChain MongoDB chat message history for a session."""
        try:
            # Get MongoDB connection string from user's database config
            mongo_uri = self.current_user.env.MONGODB_URI if hasattr(self.current_user.env, 'MONGODB_URI') else "mongodb://localhost:27017"

            return MongoDBChatMessageHistory(
                connection_string=mongo_uri,
                session_id=session_id,
                database_name=self.db.name,
                collection_name="chat_message_history"
            )
        except Exception as e:
            logger.error(f"Error creating LangChain history: {e}")
            # Fallback to simple message storage without LangChain if there's an issue
            logger.warning("Falling back to simple message storage")
            return None
    
    async def create_thread(self, request: CreateThreadRequest) -> ChatThread:
        """Create a new chat thread."""
        try:
            # Generate unique session ID for LangChain
            langchain_session_id = f"{self.current_user.id}_{uuid.uuid4()}"
            
            thread = ChatThread(
                user_id=self.current_user.id,
                title=request.title,
                description=request.description,
                context_type=request.context_type,
                knowledge_base=request.knowledge_base,
                tags=request.tags,
                langchain_session_id=langchain_session_id
            )
            
            # Insert thread into database
            result = await self.threads_collection.insert_one(thread.dict())
            
            logger.info(f"Created thread {thread.thread_id} for user {self.current_user.id}")
            return thread
            
        except Exception as e:
            logger.error(f"Error creating thread: {e}")
            raise HTTPException(status_code=500, detail="Failed to create thread")
    
    async def get_thread(self, thread_id: str) -> Optional[ChatThread]:
        """Get a specific thread by ID."""
        try:
            thread_data = await self.threads_collection.find_one({
                "thread_id": thread_id,
                "user_id": self.current_user.id,
                "status": {"$ne": ThreadStatus.DELETED}
            })
            
            if thread_data:
                return ChatThread(**thread_data)
            return None
            
        except Exception as e:
            logger.error(f"Error getting thread {thread_id}: {e}")
            return None
    
    async def list_threads(
        self, 
        limit: int = 20, 
        offset: int = 0,
        status: Optional[ThreadStatus] = None
    ) -> ThreadListResponse:
        """List user's threads with pagination."""
        try:
            # Build filter
            filter_query = {
                "user_id": self.current_user.id,
                "status": {"$ne": ThreadStatus.DELETED}
            }
            
            if status:
                filter_query["status"] = status
            
            # Get total count
            total_count = await self.threads_collection.count_documents(filter_query)
            
            # Get threads with pagination
            cursor = self.threads_collection.find(filter_query).sort("last_activity", -1).skip(offset).limit(limit)
            threads_data = await cursor.to_list(length=limit)
            
            threads = [ChatThread(**thread_data) for thread_data in threads_data]
            
            return ThreadListResponse(
                threads=threads,
                total_count=total_count,
                page=offset // limit + 1,
                page_size=limit,
                has_more=offset + limit < total_count
            )
            
        except Exception as e:
            logger.error(f"Error listing threads: {e}")
            raise HTTPException(status_code=500, detail="Failed to list threads")
    
    async def update_thread(self, thread_id: str, request: UpdateThreadRequest) -> Optional[ChatThread]:
        """Update a thread."""
        try:
            update_data = {}
            
            if request.title is not None:
                update_data["title"] = request.title
            if request.description is not None:
                update_data["description"] = request.description
            if request.status is not None:
                update_data["status"] = request.status
            if request.is_pinned is not None:
                update_data["is_pinned"] = request.is_pinned
            if request.tags is not None:
                update_data["tags"] = request.tags
            
            update_data["updated_at"] = datetime.utcnow()
            
            result = await self.threads_collection.update_one(
                {
                    "thread_id": thread_id,
                    "user_id": self.current_user.id
                },
                {"$set": update_data}
            )
            
            if result.modified_count > 0:
                return await self.get_thread(thread_id)
            return None
            
        except Exception as e:
            logger.error(f"Error updating thread {thread_id}: {e}")
            raise HTTPException(status_code=500, detail="Failed to update thread")
    
    async def delete_thread(self, thread_id: str, permanent: bool = False) -> bool:
        """Delete a thread (soft delete by default)."""
        try:
            thread = await self.get_thread(thread_id)
            if not thread:
                return False
            
            if permanent:
                # Permanent deletion
                await self.threads_collection.delete_one({
                    "thread_id": thread_id,
                    "user_id": self.current_user.id
                })
                
                # Also delete LangChain message history
                langchain_history = self._get_langchain_history(thread.langchain_session_id)
                langchain_history.clear()
                
                # Delete AI response records
                await self.ai_responses_collection.delete_many({
                    "thread_id": thread_id,
                    "user_id": self.current_user.id
                })
            else:
                # Soft delete
                await self.threads_collection.update_one(
                    {
                        "thread_id": thread_id,
                        "user_id": self.current_user.id
                    },
                    {
                        "$set": {
                            "status": ThreadStatus.DELETED,
                            "updated_at": datetime.utcnow()
                        }
                    }
                )
            
            logger.info(f"Deleted thread {thread_id} (permanent: {permanent})")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting thread {thread_id}: {e}")
            return False

    async def send_message(self, request: SendMessageRequest) -> ChatResponse:
        """Send a message to a thread and get AI response."""
        try:
            start_time = time.time()

            # Get thread
            thread = await self.get_thread(request.thread_id)
            if not thread:
                raise HTTPException(status_code=404, detail="Thread not found")

            # Store user message in our custom collection
            user_message_id = str(uuid.uuid4())
            await self._store_message(
                thread.thread_id,
                user_message_id,
                request.content,
                "user",
                request.metadata or {}
            )

            # Generate AI response using agent service directly
            ai_response_text, source_nodes = await self._call_agent_service(
                request.content,
                thread.thread_id
            )

            # Generate unique message ID for AI response
            ai_message_id = str(uuid.uuid4())

            # Store AI message in our custom collection
            await self._store_message(
                thread.thread_id,
                ai_message_id,
                ai_response_text,
                "assistant",
                {
                    "sources": source_nodes,
                    "processing_time_ms": (time.time() - start_time) * 1000,
                    "mode": "agent"
                }
            )

            # Update thread statistics
            await self._update_thread_stats(thread.thread_id)

            # Store AI response record for analytics with source nodes
            processing_time = (time.time() - start_time) * 1000
            await self._store_ai_response_record(
                thread, request, ai_response_text, source_nodes, processing_time, ai_message_id
            )

            return ChatResponse(
                thread_id=thread.thread_id,
                message_id=ai_message_id,
                user_message=request.content,
                ai_response=ai_response_text,
                sources=source_nodes,
                processing_time_ms=processing_time,
                timestamp=datetime.utcnow()
            )

        except Exception as e:
            logger.error(f"Error sending message: {e}")
            raise HTTPException(status_code=500, detail="Failed to send message")

    async def get_messages(self, request: GetMessagesRequest) -> MessageListResponse:
        """Get messages from a thread."""
        try:
            # Get thread
            thread = await self.get_thread(request.thread_id)
            if not thread:
                raise HTTPException(status_code=404, detail="Thread not found")

            # Get LangChain message history
            langchain_history = self._get_langchain_history(thread.langchain_session_id)

            # Get all messages
            all_messages = langchain_history.messages

            # Apply pagination
            start_idx = request.offset
            end_idx = start_idx + request.limit
            paginated_messages = all_messages[start_idx:end_idx]

            # Convert to response format
            messages = []
            for i, msg in enumerate(paginated_messages):
                message_response = MessageResponse(
                    id=f"{thread.langchain_session_id}_{start_idx + i}",
                    content=msg.content,
                    type="human" if isinstance(msg, HumanMessage) else "ai",
                    timestamp=datetime.utcnow(),  # LangChain doesn't store timestamps by default
                    additional_kwargs=getattr(msg, 'additional_kwargs', {})
                )
                messages.append(message_response)

            return MessageListResponse(
                messages=messages,
                total_count=len(all_messages),
                thread_id=request.thread_id,
                has_more=end_idx < len(all_messages)
            )

        except Exception as e:
            logger.error(f"Error getting messages: {e}")
            raise HTTPException(status_code=500, detail="Failed to get messages")

    async def get_chat_stats(self) -> ChatHistoryStats:
        """Get chat history statistics for the user."""
        try:
            # Get thread statistics
            total_threads = await self.threads_collection.count_documents({
                "user_id": self.current_user.id,
                "status": {"$ne": ThreadStatus.DELETED}
            })

            active_threads = await self.threads_collection.count_documents({
                "user_id": self.current_user.id,
                "status": ThreadStatus.ACTIVE
            })

            # Get AI response statistics
            total_ai_responses = await self.ai_responses_collection.count_documents({
                "user_id": self.current_user.id
            })

            # Calculate recent activity (last 24 hours)
            yesterday = datetime.utcnow() - timedelta(days=1)
            recent_activity = await self.ai_responses_collection.count_documents({
                "user_id": self.current_user.id,
                "timestamp": {"$gte": yesterday}
            })

            # Get most active thread
            pipeline = [
                {"$match": {"user_id": self.current_user.id, "status": {"$ne": ThreadStatus.DELETED}}},
                {"$sort": {"message_count": -1}},
                {"$limit": 1}
            ]
            most_active = await self.threads_collection.aggregate(pipeline).to_list(1)
            most_active_thread = most_active[0]["thread_id"] if most_active else None

            # Calculate average messages per thread
            avg_messages = 0
            if total_threads > 0:
                total_messages_pipeline = [
                    {"$match": {"user_id": self.current_user.id, "status": {"$ne": ThreadStatus.DELETED}}},
                    {"$group": {"_id": None, "total": {"$sum": "$message_count"}}}
                ]
                total_messages_result = await self.threads_collection.aggregate(total_messages_pipeline).to_list(1)
                total_messages = total_messages_result[0]["total"] if total_messages_result else 0
                avg_messages = total_messages / total_threads

            return ChatHistoryStats(
                total_threads=total_threads,
                active_threads=active_threads,
                total_messages=int(avg_messages * total_threads),
                total_ai_responses=total_ai_responses,
                avg_messages_per_thread=avg_messages,
                most_active_thread=most_active_thread,
                recent_activity_count=recent_activity
            )

        except Exception as e:
            logger.error(f"Error getting chat stats: {e}")
            raise HTTPException(status_code=500, detail="Failed to get chat statistics")

    async def _generate_ai_response(
        self,
        user_message: str,
        search_results: List[Dict[str, Any]],
        message_history: List[BaseMessage],
        mode: str = "agent"
    ) -> tuple[str, List[Dict[str, Any]]]:
        """Generate AI response based on user message and search results."""
        try:
            # Import agent service for proper integration
            from app.v1.services.agents.service import AgentService

            if mode == "agent":
                # Use the actual agent service for generating responses
                agent_service = AgentService(self.current_user)

                # Convert LangChain message history to agent format
                conversation_history = []
                for msg in message_history[-10:]:  # Last 10 messages for context
                    conversation_history.append({
                        "role": "human" if isinstance(msg, HumanMessage) else "assistant",
                        "content": msg.content
                    })

                # Generate agent response
                agent_response = await agent_service.chat_with_agent({
                    "query": user_message,
                    "conversation_history": conversation_history,
                    "language": "nepali",
                    "max_results": 5,
                    "include_sources": True
                })

                return agent_response.get("content", ""), agent_response.get("sources", [])

            else:
                # Fallback to simple template-based response
                if not search_results:
                    return "माफ गर्नुहोस्, मैले तपाईंको प्रश्नको जवाफ दिन सक्ने कुनै सान्दर्भिक दस्तावेज फेला पारेन। कृपया फरक प्रश्न सोध्नुहोस्।", []

                # Extract relevant content from search results
                context_parts = []
                for result in search_results[:3]:  # Use top 3 results
                    if 'sentences' in result:
                        for sentence in result['sentences'][:2]:  # Top 2 sentences per source
                            context_parts.append(sentence.get('text', ''))

                context = "\n".join(context_parts)

                # Simple template-based response
                response = f"""तपाईंको प्रश्न '{user_message}' को आधारमा, मैले निम्नलिखित जानकारी फेला पारेको छु:

{context}

यो जानकारी {len(search_results)} दस्तावेजहरूबाट लिइएको हो। थप विस्तृत जानकारीको लागि, कृपया स्रोत दस्तावेजहरू हेर्नुहोस्।"""

                return response, search_results

        except Exception as e:
            logger.error(f"Error generating AI response: {e}")
            return "माफ गर्नुहोस्, AI प्रतिक्रिया उत्पन्न गर्न समस्या भयो। कृपया फेरि प्रयास गर्नुहोस्।", []

    async def _update_thread_stats(self, thread_id: str):
        """Update thread statistics after adding messages."""
        try:
            await self.threads_collection.update_one(
                {"thread_id": thread_id},
                {
                    "$inc": {
                        "message_count": 2,  # User + AI message
                        "user_message_count": 1,
                        "ai_message_count": 1
                    },
                    "$set": {
                        "last_activity": datetime.now(),
                        "updated_at": datetime.now()
                    }
                }
            )
        except Exception as e:
            logger.error(f"Error updating thread stats: {e}")

    async def _store_ai_response_record(
        self,
        thread: ChatThread,
        request: SendMessageRequest,
        ai_response: str,
        source_nodes: List[Dict[str, Any]],
        processing_time: float,
        message_id: str
    ):
        """Store detailed AI response record for analytics with source nodes mapping."""
        try:
            record = AIResponseRecord(
                response_id=message_id,  # Use the same ID as the message
                user_id=self.current_user.id,
                thread_id=thread.thread_id,
                langchain_session_id=thread.langchain_session_id,
                request_data={
                    "content": request.content,
                    "max_results": request.max_results,
                    "include_sources": request.include_sources,
                    "metadata": request.metadata
                },
                response_data={
                    "content": ai_response,
                    "sources": source_nodes,
                    "message_id": message_id
                },
                processing_time_ms=processing_time,
                model_used=request.metadata.get("mode", "agent") if request.metadata else "agent",
                retrieval_context={
                    "knowledge_base": thread.knowledge_base,
                    "context_type": thread.context_type,
                    "mode": request.metadata.get("mode", "agent") if request.metadata else "agent"
                },
                knowledge_base_used=thread.knowledge_base,
                sources_found=len(source_nodes)
            )

            await self.ai_responses_collection.insert_one(record.dict())

            # Also store source nodes mapping separately for quick retrieval
            await self._store_source_nodes_mapping(message_id, source_nodes, thread.thread_id)

        except Exception as e:
            logger.error(f"Error storing AI response record: {e}")

    async def _store_source_nodes_mapping(
        self,
        message_id: str,
        source_nodes: List[Dict[str, Any]],
        thread_id: str
    ):
        """Store source nodes mapping for quick retrieval by message ID."""
        try:
            # Create a separate collection for source nodes mapping
            source_mapping = {
                "message_id": message_id,
                "thread_id": thread_id,
                "user_id": self.current_user.id,
                "source_nodes": source_nodes,
                "created_at": datetime.utcnow(),
                "node_count": len(source_nodes)
            }

            # Store in source_nodes_mapping collection
            source_collection = self.db.source_nodes_mapping
            await source_collection.insert_one(source_mapping)

            # Create index for efficient retrieval
            await source_collection.create_index([("message_id", 1)])
            await source_collection.create_index([("thread_id", 1), ("user_id", 1)])

        except Exception as e:
            logger.error(f"Error storing source nodes mapping: {e}")

    async def get_source_nodes_for_message(self, message_id: str) -> List[Dict[str, Any]]:
        """Retrieve source nodes for a specific message ID."""
        try:
            source_collection = self.db.source_nodes_mapping
            mapping = await source_collection.find_one({
                "message_id": message_id,
                "user_id": self.current_user.id
            })

            if mapping:
                return mapping.get("source_nodes", [])
            return []

        except Exception as e:
            logger.error(f"Error retrieving source nodes for message {message_id}: {e}")
            return []

    async def _store_message(
        self,
        thread_id: str,
        message_id: str,
        content: str,
        role: str,
        metadata: Dict[str, Any]
    ):
        """Store a message in our custom messages collection."""
        try:
            message_doc = {
                "message_id": message_id,
                "thread_id": thread_id,
                "user_id": self.current_user.id,
                "content": content,
                "role": role,
                "metadata": metadata,
                "timestamp": datetime.utcnow(),
                "created_at": datetime.utcnow()
            }

            # Store in messages collection
            messages_collection = self.db.chat_messages
            await messages_collection.insert_one(message_doc)

            # Create index for efficient retrieval
            await messages_collection.create_index([("thread_id", 1), ("timestamp", 1)])
            await messages_collection.create_index([("message_id", 1)])

        except Exception as e:
            logger.error(f"Error storing message: {e}")

    async def _call_agent_service(self, user_message: str, thread_id: str) -> tuple[str, List[Dict[str, Any]]]:
        """Call the agent service to get AI response and source nodes."""
        try:
            # Import agent service
            from app.v1.services.agents.service import AgentService

            # Get recent conversation history for context
            messages_collection = self.db.chat_messages
            recent_messages = await messages_collection.find({
                "thread_id": thread_id,
                "user_id": self.current_user.id
            }).sort("timestamp", -1).limit(10).to_list(10)

            # Convert to agent format
            conversation_history = []
            for msg in reversed(recent_messages):  # Reverse to get chronological order
                conversation_history.append({
                    "role": msg["role"],
                    "content": msg["content"]
                })

            # Create agent service and get response
            agent_service = AgentService(self.current_user)

            agent_request = {
                "query": user_message,
                "conversation_history": conversation_history,
                "language": "nepali",
                "max_results": 5,
                "include_sources": True
            }

            # Get agent response
            agent_response = await agent_service.chat_with_agent(agent_request)

            # Extract response and sources
            ai_response = agent_response.get("content", "माफ गर्नुहोस्, मैले तपाईंको प्रश्नको जवाफ दिन सकिन।")
            source_nodes = agent_response.get("sources", [])

            return ai_response, source_nodes

        except Exception as e:
            logger.error(f"Error calling agent service: {e}")
            # Fallback response
            return "माफ गर्नुहोस्, AI सेवामा समस्या भयो। कृपया फेरि प्रयास गर्नुहोस्।", []

    async def clear_thread_history(self, thread_id: str) -> bool:
        """Clear all messages from a thread."""
        try:
            thread = await self.get_thread(thread_id)
            if not thread:
                return False

            # Clear LangChain message history
            langchain_history = self._get_langchain_history(thread.langchain_session_id)
            langchain_history.clear()

            # Reset thread statistics
            await self.threads_collection.update_one(
                {"thread_id": thread_id},
                {
                    "$set": {
                        "message_count": 0,
                        "user_message_count": 0,
                        "ai_message_count": 0,
                        "updated_at": datetime.now()
                    }
                }
            )

            logger.info(f"Cleared history for thread {thread_id}")
            return True

        except Exception as e:
            logger.error(f"Error clearing thread history: {e}")
            return False
