# app/v1/services/chat_history/models.py

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from enum import Enum
import uuid

class MessageType(str, Enum):
    """Message type enumeration."""
    USER = "user"
    AI = "ai"
    SYSTEM = "system"

class ThreadStatus(str, Enum):
    """Thread status enumeration."""
    ACTIVE = "active"
    ARCHIVED = "archived"
    DELETED = "deleted"

class ChatMessage(BaseModel):
    """
    Individual chat message model for chat_messages collection.
    Stores both user and AI messages with proper metadata.
    """
    message_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    user_id: str = Field(..., description="User ID who owns this message")
    thread_id: str = Field(..., description="Thread ID this message belongs to")
    session_id: Optional[str] = Field(None, description="Session ID for grouping related interactions")
    message_type: MessageType = Field(..., description="Type of message (user/ai/system)")
    content: str = Field(..., description="Message content")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    # Message-specific metadata
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional message metadata")
    
    # AI-specific fields
    model_used: Optional[str] = Field(None, description="AI model used for AI messages")
    processing_time_ms: Optional[float] = Field(None, description="Processing time for AI responses")
    token_count: Optional[int] = Field(None, description="Token count for AI messages")
    
    # Source references for AI responses
    sources: List[Dict[str, Any]] = Field(default_factory=list, description="Source references for AI responses")
    
    # User interaction metadata
    user_feedback: Optional[str] = Field(None, description="User feedback on AI responses")
    rating: Optional[int] = Field(None, ge=1, le=5, description="User rating (1-5)")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class AIResponse(BaseModel):
    """
    Complete AI response model for ai_responses collection.
    Stores request-response pairs with full context.
    """
    response_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    user_id: str = Field(..., description="User ID who made the request")
    thread_id: str = Field(..., description="Thread ID this response belongs to")
    session_id: Optional[str] = Field(None, description="Session ID for grouping related interactions")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    # Request data
    request: Dict[str, Any] = Field(..., description="Complete request data")
    
    # Response data
    response: Dict[str, Any] = Field(..., description="Complete response data")
    
    # Processing metadata
    processing_time_ms: float = Field(..., description="Total processing time")
    model_used: str = Field(..., description="AI model used")
    token_usage: Optional[Dict[str, int]] = Field(None, description="Token usage statistics")
    
    # Context and retrieval metadata
    retrieval_context: Optional[Dict[str, Any]] = Field(None, description="Document retrieval context")
    knowledge_base_used: Optional[str] = Field(None, description="Knowledge base collection used")
    
    # Quality metrics
    confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="AI confidence score")
    relevance_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="Response relevance score")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class ChatThread(BaseModel):
    """
    Chat thread model for managing conversation threads.
    """
    thread_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    user_id: str = Field(..., description="User ID who owns this thread")
    title: str = Field(..., description="Thread title/name")
    description: Optional[str] = Field(None, description="Thread description")
    
    # Thread metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    last_activity: datetime = Field(default_factory=datetime.utcnow)
    
    # Thread configuration
    status: ThreadStatus = Field(default=ThreadStatus.ACTIVE)
    is_pinned: bool = Field(default=False, description="Whether thread is pinned")
    tags: List[str] = Field(default_factory=list, description="Thread tags for organization")
    
    # Context and settings
    context_type: Optional[str] = Field(None, description="Type of context (legal, general, etc.)")
    knowledge_base: Optional[str] = Field(None, description="Associated knowledge base")
    retriever_settings: Dict[str, Any] = Field(default_factory=dict, description="Retriever configuration")
    
    # Statistics
    message_count: int = Field(default=0, description="Total messages in thread")
    ai_message_count: int = Field(default=0, description="AI messages in thread")
    user_message_count: int = Field(default=0, description="User messages in thread")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

# Request/Response models for API endpoints

class CreateThreadRequest(BaseModel):
    """Request model for creating a new thread."""
    title: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    context_type: Optional[str] = Field(None)
    knowledge_base: Optional[str] = Field(None)
    tags: List[str] = Field(default_factory=list)

class UpdateThreadRequest(BaseModel):
    """Request model for updating a thread."""
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    status: Optional[ThreadStatus] = Field(None)
    is_pinned: Optional[bool] = Field(None)
    tags: Optional[List[str]] = Field(None)

class SendMessageRequest(BaseModel):
    """Request model for sending a message."""
    thread_id: str = Field(..., description="Thread ID to send message to")
    content: str = Field(..., min_length=1, description="Message content")
    message_type: MessageType = Field(default=MessageType.USER)
    session_id: Optional[str] = Field(None)
    metadata: Dict[str, Any] = Field(default_factory=dict)

class GetMessagesRequest(BaseModel):
    """Request model for retrieving messages."""
    thread_id: str = Field(..., description="Thread ID to get messages from")
    limit: int = Field(default=50, ge=1, le=200, description="Number of messages to retrieve")
    offset: int = Field(default=0, ge=0, description="Offset for pagination")
    message_type: Optional[MessageType] = Field(None, description="Filter by message type")
    since: Optional[datetime] = Field(None, description="Get messages since this timestamp")

class ThreadListResponse(BaseModel):
    """Response model for thread listing."""
    threads: List[ChatThread]
    total_count: int
    page: int
    page_size: int
    has_more: bool

class MessageListResponse(BaseModel):
    """Response model for message listing."""
    messages: List[ChatMessage]
    total_count: int
    thread_id: str
    has_more: bool

class ChatHistoryStats(BaseModel):
    """Chat history statistics model."""
    total_threads: int
    active_threads: int
    total_messages: int
    total_ai_responses: int
    avg_messages_per_thread: float
    most_active_thread: Optional[str]
    recent_activity_count: int  # Messages in last 24 hours

class ThreadSummary(BaseModel):
    """Thread summary model for quick overview."""
    thread_id: str
    title: str
    message_count: int
    last_activity: datetime
    last_message_preview: Optional[str] = Field(None, max_length=100)
    unread_count: int = Field(default=0)
