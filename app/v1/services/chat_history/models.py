# app/v1/services/chat_history/models.py

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum
import uuid

class ThreadStatus(str, Enum):
    """Thread status enumeration."""
    ACTIVE = "active"
    ARCHIVED = "archived"
    DELETED = "deleted"

class ChatThread(BaseModel):
    """
    Chat thread model for managing conversation threads.
    Integrates with LangChain MongoDB chat message history.
    """
    thread_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    user_id: str = Field(..., description="User ID who owns this thread")
    title: str = Field(..., description="Thread title/name")
    description: Optional[str] = Field(None, description="Thread description")
    
    # Thread metadata
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
    last_activity: datetime = Field(default_factory=datetime.utcnow)
    
    # Thread configuration
    status: ThreadStatus = Field(default=ThreadStatus.ACTIVE)
    is_pinned: bool = Field(default=False, description="Whether thread is pinned")
    tags: List[str] = Field(default_factory=list, description="Thread tags for organization")
    
    # Context and settings
    context_type: Optional[str] = Field(None, description="Type of context (legal, general, etc.)")
    knowledge_base: Optional[str] = Field(None, description="Associated knowledge base")
    retriever_settings: Dict[str, Any] = Field(default_factory=dict, description="Retriever configuration")
    
    # LangChain integration
    langchain_session_id: str = Field(..., description="LangChain session ID for message history")
    
    # Statistics
    message_count: int = Field(default=0, description="Total messages in thread")
    ai_message_count: int = Field(default=0, description="AI messages in thread")
    user_message_count: int = Field(default=0, description="User messages in thread")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class AIResponseRecord(BaseModel):
    """
    AI response record for detailed analytics and debugging.
    Complements LangChain message history with additional metadata.
    """
    response_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    user_id: str = Field(..., description="User ID who made the request")
    thread_id: str = Field(..., description="Thread ID this response belongs to")
    langchain_session_id: str = Field(..., description="LangChain session ID")
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    
    # Request/Response data (stored separately from LangChain for analytics)
    request_data: Dict[str, Any] = Field(..., description="Complete request data")
    response_data: Dict[str, Any] = Field(..., description="Complete response data")
    
    # Processing metadata
    processing_time_ms: float = Field(..., description="Total processing time")
    model_used: str = Field(..., description="AI model used")
    token_usage: Optional[Dict[str, int]] = Field(None, description="Token usage statistics")
    
    # Context and retrieval metadata
    retrieval_context: Optional[Dict[str, Any]] = Field(None, description="Document retrieval context")
    knowledge_base_used: Optional[str] = Field(None, description="Knowledge base collection used")
    sources_found: int = Field(default=0, description="Number of sources found")
    
    # Quality metrics
    confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="AI confidence score")
    relevance_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="Response relevance score")
    
    # User feedback
    user_rating: Optional[int] = Field(None, ge=1, le=5, description="User rating (1-5)")
    user_feedback: Optional[str] = Field(None, description="User feedback text")
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

# Request/Response models for API endpoints

class CreateThreadRequest(BaseModel):
    """Request model for creating a new thread."""
    title: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    context_type: Optional[str] = Field("legal", description="Context type (legal, general, etc.)")
    knowledge_base: Optional[str] = Field(None, description="Knowledge base to use")
    tags: List[str] = Field(default_factory=list)

class UpdateThreadRequest(BaseModel):
    """Request model for updating a thread."""
    title: Optional[str] = Field(None, min_length=1, max_length=200)
    description: Optional[str] = Field(None, max_length=1000)
    status: Optional[ThreadStatus] = Field(None)
    is_pinned: Optional[bool] = Field(None)
    tags: Optional[List[str]] = Field(None)

class SendMessageRequest(BaseModel):
    """Request model for sending a message to a thread."""
    thread_id: str = Field(..., description="Thread ID to send message to")
    content: str = Field(..., min_length=1, description="Message content")
    max_results: int = Field(default=5, ge=1, le=20, description="Max search results for AI response")
    include_sources: bool = Field(default=True, description="Include source references")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")

class GetMessagesRequest(BaseModel):
    """Request model for retrieving messages from a thread."""
    thread_id: str = Field(..., description="Thread ID to get messages from")
    limit: int = Field(default=50, ge=1, le=200, description="Number of messages to retrieve")
    offset: int = Field(default=0, ge=0, description="Offset for pagination")

class ThreadListResponse(BaseModel):
    """Response model for thread listing."""
    threads: List[ChatThread]
    total_count: int
    page: int
    page_size: int
    has_more: bool

class MessageResponse(BaseModel):
    """Response model for individual messages."""
    id: str
    content: str
    type: str  # "human" or "ai" (LangChain format)
    timestamp: datetime
    additional_kwargs: Dict[str, Any] = Field(default_factory=dict)

class MessageListResponse(BaseModel):
    """Response model for message listing."""
    messages: List[MessageResponse]
    total_count: int
    thread_id: str
    has_more: bool

class ChatResponse(BaseModel):
    """Response model for chat interactions."""
    thread_id: str
    message_id: str
    user_message: str
    ai_response: str
    sources: List[Dict[str, Any]] = Field(default_factory=list)
    processing_time_ms: float
    timestamp: datetime

class ChatHistoryStats(BaseModel):
    """Chat history statistics model."""
    total_threads: int
    active_threads: int
    total_messages: int
    total_ai_responses: int
    avg_messages_per_thread: float
    most_active_thread: Optional[str]
    recent_activity_count: int  # Messages in last 24 hours
