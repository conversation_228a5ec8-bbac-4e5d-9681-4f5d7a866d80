# app/v1/services/chat_history/routes.py

from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from app.shared.database.models import UserTenantDB
from app.shared.security.dependencies import get_current_user
from app.shared.utils.logger import setup_new_logging
from .simple_service import ChatHistoryService

router = APIRouter(prefix="/chat-history", tags=["Chat History"])
logger = setup_new_logging(__name__)

# Simple response models
class ChatResponse(BaseModel):
    user_message: str
    ai_response: str
    sources: List[Dict[str, Any]]
    message_id: str
    timestamp: str

class SourceNodesResponse(BaseModel):
    message_id: str
    source_nodes: List[Dict[str, Any]]
    found: bool
    count: int

class MessageResponse(BaseModel):
    id: str
    content: str
    role: str
    timestamp: str
    metadata: Optional[Dict[str, Any]] = None

class MessageListResponse(BaseModel):
    messages: List[MessageResponse]
    total_count: int
    has_more: bool

@router.post("/chat", response_model=ChatResponse)
async def simple_chat(
    message: str,
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Simple chat endpoint - send message and get AI response.
    Backend handles all chat history insertion automatically.
    """
    service = ChatHistoryService(current_user)
    return await service.simple_chat(message)

@router.get("/messages/{message_id}/sources", response_model=SourceNodesResponse)
async def get_message_sources(
    message_id: str,
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Get source nodes for a specific message.
    """
    service = ChatHistoryService(current_user)
    
    try:
        source_nodes = await service.get_source_nodes_for_message(message_id)
        
        return SourceNodesResponse(
            message_id=message_id,
            source_nodes=source_nodes,
            found=len(source_nodes) > 0,
            count=len(source_nodes)
        )
        
    except Exception as e:
        logger.error(f"Error retrieving source nodes for message {message_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve source nodes")

@router.get("/messages", response_model=MessageListResponse)
async def get_messages(
    limit: int = Query(50, ge=1, le=200, description="Number of messages to retrieve"),
    offset: int = Query(0, ge=0, description="Offset for pagination"),
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Get chat history messages with pagination.
    """
    service = ChatHistoryService(current_user)
    
    try:
        messages, total_count, has_more = await service.get_messages(limit, offset)
        
        return MessageListResponse(
            messages=messages,
            total_count=total_count,
            has_more=has_more
        )
        
    except Exception as e:
        logger.error(f"Error retrieving messages: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve messages")
