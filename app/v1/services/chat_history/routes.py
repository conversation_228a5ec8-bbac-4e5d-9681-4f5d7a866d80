# app/v1/services/chat_history/routes.py

from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Optional

from app.shared.database.models import UserTenantDB
from app.shared.security.dependencies import get_current_user
from app.shared.utils.logger import setup_new_logging
from .service import ChatHistoryService
from .models import (
    CreateThreadRequest, UpdateThreadRequest, SendMessageRequest, GetMessagesRequest,
    ThreadListResponse, MessageListResponse, ChatResponse, ChatHistoryStats,
    ThreadStatus, ChatThread
)

router = APIRouter(prefix="/chat-history", tags=["Chat History"])
logger = setup_new_logging(__name__)

@router.post("/threads", response_model=ChatThread)
async def create_thread(
    request: CreateThreadRequest,
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Create a new chat thread.
    """
    service = ChatHistoryService(current_user)
    return await service.create_thread(request)

@router.get("/threads", response_model=ThreadListResponse)
async def list_threads(
    limit: int = Query(20, ge=1, le=100, description="Number of threads per page"),
    offset: int = Query(0, ge=0, description="Offset for pagination"),
    status: Optional[ThreadStatus] = Query(None, description="Filter by thread status"),
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    List user's chat threads with pagination.
    """
    service = ChatHistoryService(current_user)
    return await service.list_threads(limit=limit, offset=offset, status=status)

@router.get("/threads/{thread_id}", response_model=ChatThread)
async def get_thread(
    thread_id: str,
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Get a specific thread by ID.
    """
    service = ChatHistoryService(current_user)
    thread = await service.get_thread(thread_id)
    if not thread:
        raise HTTPException(status_code=404, detail="Thread not found")
    return thread

@router.put("/threads/{thread_id}", response_model=ChatThread)
async def update_thread(
    thread_id: str,
    request: UpdateThreadRequest,
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Update a thread's metadata.
    """
    service = ChatHistoryService(current_user)
    thread = await service.update_thread(thread_id, request)
    if not thread:
        raise HTTPException(status_code=404, detail="Thread not found")
    return thread

@router.delete("/threads/{thread_id}")
async def delete_thread(
    thread_id: str,
    permanent: bool = Query(False, description="Permanently delete thread"),
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Delete a thread (soft delete by default).
    """
    service = ChatHistoryService(current_user)
    success = await service.delete_thread(thread_id, permanent=permanent)
    if not success:
        raise HTTPException(status_code=404, detail="Thread not found")
    return {"message": "Thread deleted successfully", "permanent": permanent}

@router.post("/threads/{thread_id}/messages", response_model=ChatResponse)
async def send_message(
    thread_id: str,
    request: SendMessageRequest,
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Send a message to a thread and get AI response.
    """
    # Ensure thread_id matches
    request.thread_id = thread_id

    service = ChatHistoryService(current_user)
    return await service.send_message(request)

@router.post("/chat", response_model=ChatResponse)
async def simple_chat(
    message: str,
    thread_id: Optional[str] = None,
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Simple chat endpoint - send message and get AI response.
    Backend handles all chat history insertion automatically.
    """
    service = ChatHistoryService(current_user)

    # Get or create thread
    if thread_id:
        thread = await service.get_thread(thread_id)
        if not thread:
            raise HTTPException(status_code=404, detail="Thread not found")
    else:
        # Create new thread
        thread = await service.create_thread(CreateThreadRequest(
            title=f"Chat {datetime.utcnow().strftime('%Y-%m-%d %H:%M')}",
            context_type="legal",
            description="Legal assistant conversation"
        ))

    # Send message
    request = SendMessageRequest(
        thread_id=thread.thread_id,
        content=message,
        max_results=5,
        include_sources=True,
        metadata={"mode": "agent"}
    )

    return await service.send_message(request)

@router.get("/threads/{thread_id}/messages", response_model=MessageListResponse)
async def get_messages(
    thread_id: str,
    limit: int = Query(50, ge=1, le=200, description="Number of messages to retrieve"),
    offset: int = Query(0, ge=0, description="Offset for pagination"),
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Get messages from a thread with pagination.
    """
    request = GetMessagesRequest(
        thread_id=thread_id,
        limit=limit,
        offset=offset
    )
    
    service = ChatHistoryService(current_user)
    return await service.get_messages(request)

@router.delete("/threads/{thread_id}/messages")
async def clear_thread_history(
    thread_id: str,
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Clear all messages from a thread.
    """
    service = ChatHistoryService(current_user)
    success = await service.clear_thread_history(thread_id)
    if not success:
        raise HTTPException(status_code=404, detail="Thread not found")
    return {"message": "Thread history cleared successfully"}

@router.get("/stats", response_model=ChatHistoryStats)
async def get_chat_stats(
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Get chat history statistics for the current user.
    """
    service = ChatHistoryService(current_user)
    return await service.get_chat_stats()

# Additional utility endpoints

@router.post("/threads/{thread_id}/archive")
async def archive_thread(
    thread_id: str,
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Archive a thread.
    """
    service = ChatHistoryService(current_user)
    request = UpdateThreadRequest(status=ThreadStatus.ARCHIVED)
    thread = await service.update_thread(thread_id, request)
    if not thread:
        raise HTTPException(status_code=404, detail="Thread not found")
    return {"message": "Thread archived successfully"}

@router.post("/threads/{thread_id}/unarchive")
async def unarchive_thread(
    thread_id: str,
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Unarchive a thread.
    """
    service = ChatHistoryService(current_user)
    request = UpdateThreadRequest(status=ThreadStatus.ACTIVE)
    thread = await service.update_thread(thread_id, request)
    if not thread:
        raise HTTPException(status_code=404, detail="Thread not found")
    return {"message": "Thread unarchived successfully"}

@router.post("/threads/{thread_id}/pin")
async def pin_thread(
    thread_id: str,
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Pin a thread.
    """
    service = ChatHistoryService(current_user)
    request = UpdateThreadRequest(is_pinned=True)
    thread = await service.update_thread(thread_id, request)
    if not thread:
        raise HTTPException(status_code=404, detail="Thread not found")
    return {"message": "Thread pinned successfully"}

@router.post("/threads/{thread_id}/unpin")
async def unpin_thread(
    thread_id: str,
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Unpin a thread.
    """
    service = ChatHistoryService(current_user)
    request = UpdateThreadRequest(is_pinned=False)
    thread = await service.update_thread(thread_id, request)
    if not thread:
        raise HTTPException(status_code=404, detail="Thread not found")
    return {"message": "Thread unpinned successfully"}

@router.get("/threads/search")
async def search_threads(
    query: Optional[str] = Query(None, description="Search query for thread titles/descriptions"),
    tags: Optional[str] = Query(None, description="Comma-separated tags to filter by"),
    context_type: Optional[str] = Query(None, description="Filter by context type"),
    limit: int = Query(20, ge=1, le=100),
    offset: int = Query(0, ge=0),
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Search threads by title, description, tags, or context type.
    """
    service = ChatHistoryService(current_user)

    # Build search filter
    filter_query = {
        "user_id": current_user.id,
        "status": {"$ne": ThreadStatus.DELETED}
    }

    if query:
        filter_query["$or"] = [
            {"title": {"$regex": query, "$options": "i"}},
            {"description": {"$regex": query, "$options": "i"}}
        ]

    if tags:
        tag_list = [tag.strip() for tag in tags.split(",")]
        filter_query["tags"] = {"$in": tag_list}

    if context_type:
        filter_query["context_type"] = context_type

    try:
        # Get total count
        total_count = await service.threads_collection.count_documents(filter_query)

        # Get threads with pagination
        cursor = service.threads_collection.find(filter_query).sort("last_activity", -1).skip(offset).limit(limit)
        threads_data = await cursor.to_list(length=limit)

        threads = [ChatThread(**thread_data) for thread_data in threads_data]

        return ThreadListResponse(
            threads=threads,
            total_count=total_count,
            page=offset // limit + 1,
            page_size=limit,
            has_more=offset + limit < total_count
        )

    except Exception as e:
        logger.error(f"Error searching threads: {e}")
        raise HTTPException(status_code=500, detail="Failed to search threads")

@router.get("/messages/{message_id}/sources")
async def get_message_sources(
    message_id: str,
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Get source nodes for a specific message.
    This endpoint allows retrieving the source documents used to generate an AI response.
    """
    service = ChatHistoryService(current_user)

    try:
        # Get source nodes for the message
        source_nodes = await service.get_source_nodes_for_message(message_id)

        if not source_nodes:
            return {
                "message_id": message_id,
                "source_nodes": [],
                "found": False,
                "count": 0
            }

        return {
            "message_id": message_id,
            "source_nodes": source_nodes,
            "found": True,
            "count": len(source_nodes)
        }

    except Exception as e:
        logger.error(f"Error retrieving source nodes for message {message_id}: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve source nodes")
