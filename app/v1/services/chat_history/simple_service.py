# app/v1/services/chat_history/simple_service.py

import uuid
from typing import List, Dict, Any, <PERSON><PERSON>
from datetime import datetime
from fastapi import HTT<PERSON>Exception
from pymongo.collection import Collection

from app.shared.database.models import UserTenantDB
from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)

class ChatHistoryService:
    """
    Simplified chat history service that focuses on essential functionality:
    - Store user messages and AI responses
    - Integrate with agent service
    - Retrieve source nodes for messages
    """
    
    def __init__(self, current_user: UserTenantDB):
        self.current_user = current_user
        self.db = current_user.adb
        
        # Collections
        self.messages_collection: Collection = self.db.chat_messages
        self.source_nodes_collection: Collection = self.db.source_nodes_mapping
        
        # Create indexes
        self._create_indexes()
    
    def _create_indexes(self):
        """Create necessary indexes for efficient queries."""
        try:
            # Message indexes
            self.messages_collection.create_index([("user_id", 1), ("timestamp", -1)])
            self.messages_collection.create_index([("message_id", 1)])
            
            # Source nodes indexes
            self.source_nodes_collection.create_index([("message_id", 1)])
            self.source_nodes_collection.create_index([("user_id", 1)])
            
            logger.debug("Created chat history indexes")
        except Exception as e:
            logger.warning(f"Error creating indexes: {e}")
    
    async def simple_chat(self, message: str) -> Dict[str, Any]:
        """
        Simple chat method that handles the entire flow:
        1. Store user message
        2. Call agent service
        3. Store AI response with source nodes
        4. Return response
        """
        try:
            start_time = datetime.utcnow()
            
            # Generate message IDs
            user_message_id = str(uuid.uuid4())
            ai_message_id = str(uuid.uuid4())
            
            # Store user message
            await self._store_message(
                message_id=user_message_id,
                content=message,
                role="user",
                metadata={}
            )
            
            # Call agent service to get AI response
            ai_response, source_nodes = await self._call_agent_service(message)
            
            # Store AI response
            await self._store_message(
                message_id=ai_message_id,
                content=ai_response,
                role="assistant",
                metadata={"sources": source_nodes}
            )
            
            # Store source nodes mapping
            if source_nodes:
                await self._store_source_nodes(ai_message_id, source_nodes)
            
            return {
                "user_message": message,
                "ai_response": ai_response,
                "sources": source_nodes,
                "message_id": ai_message_id,
                "timestamp": start_time.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in simple_chat: {e}")
            raise HTTPException(status_code=500, detail="Failed to process chat message")
    
    async def _store_message(
        self, 
        message_id: str, 
        content: str, 
        role: str, 
        metadata: Dict[str, Any]
    ):
        """Store a message in the database."""
        try:
            message_doc = {
                "message_id": message_id,
                "user_id": self.current_user.id,
                "content": content,
                "role": role,
                "metadata": metadata,
                "timestamp": datetime.utcnow(),
                "created_at": datetime.utcnow()
            }
            
            await self.messages_collection.insert_one(message_doc)
            
        except Exception as e:
            logger.error(f"Error storing message: {e}")
            raise
    
    async def _call_agent_service(self, user_message: str) -> Tuple[str, List[Dict[str, Any]]]:
        """Call the agent service to get AI response and source nodes."""
        try:
            # Import agent service and types
            from app.v1.services.agents.service import AgentService
            from app.v1.services.agents.types import AgentContext

            # Get recent conversation history for context
            recent_messages = await self.messages_collection.find({
                "user_id": self.current_user.id
            }).sort("timestamp", -1).limit(10).to_list(10)

            # Convert to conversation history format
            conversation_history = []
            for msg in reversed(recent_messages):  # Reverse to get chronological order
                conversation_history.append({
                    "role": msg["role"],
                    "content": msg["content"]
                })

            # Create agent service (no parameters needed)
            agent_service = AgentService()

            # Create agent context
            agent_context = AgentContext(
                user_id=self.current_user.id,
                tenant_id=self.current_user.tenant_id,
                query=user_message,
                conversation_history=conversation_history,
                session_id=f"chat_{uuid.uuid4()}",
                language="nepali",
                max_results=5,
                include_sources=True
            )

            # Get agent response
            agent_response = await agent_service.process_query(agent_context, self.current_user)

            # Extract response and sources
            ai_response = agent_response.content if agent_response.content else "माफ गर्नुहोस्, मैले तपाईंको प्रश्नको जवाफ दिन सकिन।"
            source_nodes = agent_response.sources if agent_response.sources else []

            return ai_response, source_nodes
            
        except Exception as e:
            logger.error(f"Error calling agent service: {e}")
            # Fallback response
            return "माफ गर्नुहोस्, AI सेवामा समस्या भयो। कृपया फेरि प्रयास गर्नुहोस्।", []
    
    async def _store_source_nodes(self, message_id: str, source_nodes: List[Dict[str, Any]]):
        """Store source nodes mapping for a message."""
        try:
            source_mapping = {
                "message_id": message_id,
                "user_id": self.current_user.id,
                "source_nodes": source_nodes,
                "created_at": datetime.utcnow(),
                "node_count": len(source_nodes)
            }
            
            await self.source_nodes_collection.insert_one(source_mapping)
            
        except Exception as e:
            logger.error(f"Error storing source nodes: {e}")
    
    async def get_source_nodes_for_message(self, message_id: str) -> List[Dict[str, Any]]:
        """Retrieve source nodes for a specific message ID."""
        try:
            mapping = await self.source_nodes_collection.find_one({
                "message_id": message_id,
                "user_id": self.current_user.id
            })
            
            if mapping:
                return mapping.get("source_nodes", [])
            return []
            
        except Exception as e:
            logger.error(f"Error retrieving source nodes for message {message_id}: {e}")
            return []
    
    async def get_messages(self, limit: int, offset: int) -> Tuple[List[Dict[str, Any]], int, bool]:
        """Get chat history messages with pagination."""
        try:
            # Get total count
            total_count = await self.messages_collection.count_documents({
                "user_id": self.current_user.id
            })
            
            # Get messages with pagination
            cursor = self.messages_collection.find({
                "user_id": self.current_user.id
            }).sort("timestamp", -1).skip(offset).limit(limit)
            
            messages_data = await cursor.to_list(length=limit)
            
            # Convert to response format
            messages = []
            for msg in messages_data:
                messages.append({
                    "id": msg["message_id"],
                    "content": msg["content"],
                    "role": msg["role"],
                    "timestamp": msg["timestamp"].isoformat(),
                    "metadata": msg.get("metadata", {})
                })
            
            has_more = offset + limit < total_count
            
            return messages, total_count, has_more
            
        except Exception as e:
            logger.error(f"Error getting messages: {e}")
            return [], 0, False
