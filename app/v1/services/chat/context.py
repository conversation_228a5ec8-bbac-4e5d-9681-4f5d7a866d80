# app/v1/services/chat/context.py

import torch
import torch.nn as nn
from typing import List, Dict, Any, Optional
import numpy as np
from datetime import datetime

from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)

class SimpleLSTMContext:
    """
    Simple LSTM-based context handler for conversation understanding.
    Helps maintain conversation context and improve response relevance.
    """
    
    def __init__(self, vocab_size: int = 10000, embedding_dim: int = 128, hidden_dim: int = 256):
        self.vocab_size = vocab_size
        self.embedding_dim = embedding_dim
        self.hidden_dim = hidden_dim
        
        # Simple word-to-index mapping
        self.word_to_idx = {}
        self.idx_to_word = {}
        self.vocab_count = 0
        
        # Initialize LSTM model
        self.model = self._create_model()
        self.hidden_state = None
        
        logger.info("Initialized SimpleLSTMContext")
    
    def _create_model(self):
        """Create a simple LSTM model for context understanding."""
        
        class ContextLSTM(nn.Module):
            def __init__(self, vocab_size, embedding_dim, hidden_dim):
                super().__init__()
                self.embedding = nn.Embedding(vocab_size, embedding_dim)
                self.lstm = nn.LSTM(embedding_dim, hidden_dim, batch_first=True)
                self.output_layer = nn.Linear(hidden_dim, vocab_size)
                
            def forward(self, x, hidden=None):
                embedded = self.embedding(x)
                lstm_out, hidden = self.lstm(embedded, hidden)
                output = self.output_layer(lstm_out)
                return output, hidden
        
        return ContextLSTM(self.vocab_size, self.embedding_dim, self.hidden_dim)
    
    def _tokenize(self, text: str) -> List[int]:
        """Simple tokenization - convert text to indices."""
        words = text.lower().split()
        indices = []
        
        for word in words:
            if word not in self.word_to_idx:
                if self.vocab_count < self.vocab_size:
                    self.word_to_idx[word] = self.vocab_count
                    self.idx_to_word[self.vocab_count] = word
                    self.vocab_count += 1
                else:
                    # Use unknown token
                    word = "<UNK>"
                    if word not in self.word_to_idx:
                        self.word_to_idx[word] = 0
                        self.idx_to_word[0] = word
            
            indices.append(self.word_to_idx[word])
        
        return indices
    
    def update_context(self, message: str, role: str = "user") -> Dict[str, Any]:
        """
        Update the LSTM context with a new message.
        
        Args:
            message: The message content
            role: The role (user/assistant)
            
        Returns:
            Context information including attention weights and relevance scores
        """
        try:
            # Tokenize the message
            tokens = self._tokenize(message)
            
            if not tokens:
                return {"context_vector": None, "attention_weights": [], "relevance_score": 0.0}
            
            # Convert to tensor
            input_tensor = torch.tensor([tokens], dtype=torch.long)
            
            # Forward pass through LSTM
            with torch.no_grad():
                output, self.hidden_state = self.model(input_tensor, self.hidden_state)
            
            # Extract context vector (last hidden state)
            context_vector = self.hidden_state[0][-1].numpy().tolist()
            
            # Calculate simple attention weights (based on output probabilities)
            attention_weights = torch.softmax(output[0], dim=-1).mean(dim=0).numpy().tolist()
            
            # Calculate relevance score (based on hidden state magnitude)
            relevance_score = float(torch.norm(self.hidden_state[0][-1]).item())
            
            return {
                "context_vector": context_vector,
                "attention_weights": attention_weights[:10],  # Top 10 for brevity
                "relevance_score": min(relevance_score / 10.0, 1.0),  # Normalize to 0-1
                "message_length": len(tokens),
                "vocab_size": self.vocab_count
            }
            
        except Exception as e:
            logger.error(f"Error updating context: {e}")
            return {"context_vector": None, "attention_weights": [], "relevance_score": 0.0}
    
    def get_context_summary(self) -> Dict[str, Any]:
        """Get a summary of the current context state."""
        try:
            if self.hidden_state is None:
                return {"status": "no_context", "summary": "कुनै सन्दर्भ उपलब्ध छैन"}
            
            # Get context strength
            context_strength = float(torch.norm(self.hidden_state[0][-1]).item())
            
            # Determine context quality
            if context_strength > 15:
                quality = "उत्कृष्ट"
                description = "कुराकानीको सन्दर्भ राम्रोसँग बुझिएको छ"
            elif context_strength > 10:
                quality = "राम्रो"
                description = "कुराकानीको सन्दर्भ बुझिएको छ"
            elif context_strength > 5:
                quality = "मध्यम"
                description = "केही सन्दर्भ जानकारी उपलब्ध छ"
            else:
                quality = "कम"
                description = "सीमित सन्दर्भ जानकारी"
            
            return {
                "status": "active",
                "quality": quality,
                "description": description,
                "context_strength": context_strength,
                "vocab_size": self.vocab_count,
                "summary": f"सन्दर्भ गुणस्तर: {quality} - {description}"
            }
            
        except Exception as e:
            logger.error(f"Error getting context summary: {e}")
            return {"status": "error", "summary": "सन्दर्भ जानकारी प्राप्त गर्न समस्या"}
    
    def reset_context(self):
        """Reset the LSTM context state."""
        self.hidden_state = None
        logger.info("Reset LSTM context state")
    
    def get_relevant_keywords(self, top_k: int = 5) -> List[str]:
        """Get the most relevant keywords from the current context."""
        try:
            if self.hidden_state is None or self.vocab_count == 0:
                return []
            
            # Get the last output and find top words
            with torch.no_grad():
                # Create a dummy input to get output
                dummy_input = torch.tensor([[0]], dtype=torch.long)
                output, _ = self.model(dummy_input, self.hidden_state)
                
                # Get top-k most probable words
                top_indices = torch.topk(output[0, -1], top_k).indices.tolist()
                
                keywords = []
                for idx in top_indices:
                    if idx in self.idx_to_word:
                        keywords.append(self.idx_to_word[idx])
                
                return keywords
                
        except Exception as e:
            logger.error(f"Error getting relevant keywords: {e}")
            return []

class ContextManager:
    """
    Manager for handling conversation context across sessions.
    Integrates LSTM context with session management.
    """
    
    def __init__(self):
        self.session_contexts = {}
        logger.info("Initialized ContextManager")
    
    def get_context(self, session_id: str) -> SimpleLSTMContext:
        """Get or create LSTM context for a session."""
        if session_id not in self.session_contexts:
            self.session_contexts[session_id] = SimpleLSTMContext()
        return self.session_contexts[session_id]
    
    def update_session_context(self, session_id: str, message: str, role: str) -> Dict[str, Any]:
        """Update context for a specific session."""
        context = self.get_context(session_id)
        return context.update_context(message, role)
    
    def get_session_summary(self, session_id: str) -> Dict[str, Any]:
        """Get context summary for a session."""
        if session_id in self.session_contexts:
            return self.session_contexts[session_id].get_context_summary()
        return {"status": "no_session", "summary": "सत्र फेला परेन"}
    
    def cleanup_old_sessions(self, max_sessions: int = 100):
        """Clean up old sessions to prevent memory issues."""
        if len(self.session_contexts) > max_sessions:
            # Keep only the most recent sessions
            sessions_to_remove = list(self.session_contexts.keys())[:-max_sessions]
            for session_id in sessions_to_remove:
                del self.session_contexts[session_id]
            logger.info(f"Cleaned up {len(sessions_to_remove)} old context sessions")
