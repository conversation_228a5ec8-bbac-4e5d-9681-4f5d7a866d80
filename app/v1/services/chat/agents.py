# app/v1/services/chat/agents.py

import asyncio
from typing import List, Dict, Any, Optional, Tu<PERSON>
from datetime import datetime

from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate, MessagesPlaceholder
from langchain_core.output_parsers import StrOutputParser
from langchain_core.runnables import RunnablePassthrough, RunnableLambda
from langchain_openai import Chat<PERSON>penA<PERSON>

from app.shared.database.models import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from app.v1.services.knowledge_base.service import KnowledgeBaseService

logger = setup_new_logging(__name__)

class DocumentRetrievalAgent:
    """
    Sub-agent responsible for retrieving relevant documents from the knowledge base.
    Uses vector similarity search and semantic ranking.
    """
    
    def __init__(self, current_user: UserTenantDB):
        self.current_user = current_user
        self.kb_service = KnowledgeBaseService(current_user)
    
    async def retrieve_documents(
        self, 
        query: str, 
        max_results: int = 5,
        include_metadata: bool = True
    ) -> List[Dict[str, Any]]:
        """Retrieve relevant documents for the query."""
        try:
            logger.info(f"Retrieving documents for query: {query[:100]}...")
            
            # Use knowledge base service for retrieval
            results = await self.kb_service.retrieve_source_nodes(query, max_results)
            
            # Format results for the main agent
            formatted_results = []
            for result in results:
                formatted_results.append({
                    "id": result.get("id", "unknown"),
                    "content": result.get("text", ""),
                    "metadata": result.get("metadata", {}),
                    "score": result.get("score", 0.0)
                })
            
            logger.info(f"Retrieved {len(formatted_results)} documents")
            return formatted_results
            
        except Exception as e:
            logger.error(f"Error retrieving documents: {e}")
            return []

class LegalAssistantAgent:
    """
    Main agent that processes user queries and generates responses.
    Uses LangChain with OpenAI and integrates with document retrieval.
    """
    
    def __init__(self, current_user: UserTenantDB):
        self.current_user = current_user
        self.retrieval_agent = DocumentRetrievalAgent(current_user)
        
        # Initialize LLM
        self.llm = ChatOpenAI(
            model="gpt-4o-mini",
            temperature=0.1,
            max_tokens=2000
        )
        
        # Create prompt template
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", self._get_system_prompt()),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", "{query}"),
            ("system", "संदर्भ दस्तावेजहरू:\n{context}")
        ])
        
        # Create the chain
        self.chain = (
            RunnablePassthrough.assign(
                context=RunnableLambda(self._format_context)
            )
            | self.prompt
            | self.llm
            | StrOutputParser()
        )
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for the legal assistant."""
        return """तपाईं एक नेपाली कानुनी सहायक हुनुहुन्छ। तपाईंको काम भनेको:

1. नेपाली कानुनी प्रश्नहरूको सटीक र उपयोगी जवाफ दिनु
2. प्रदान गरिएका संदर्भ दस्तावेजहरूको आधारमा जानकारी दिनु
3. कानुनी सल्लाह नभएर सामान्य जानकारी मात्र प्रदान गर्नु
4. नेपाली भाषामा स्पष्ट र बुझ्न सकिने भाषामा जवाफ दिनु

महत्वपूर्ण निर्देशनहरू:
- सधैं संदर्भ दस्तावेजहरूको आधारमा जवाफ दिनुहोस्
- यदि जानकारी उपलब्ध छैन भने स्पष्ट रूपमा भन्नुहोस्
- कानुनी सल्लाहको लागि योग्य वकिलसँग सम्पर्क गर्न सुझाव दिनुहोस्
- तथ्यपरक र निष्पक्ष जानकारी प्रदान गर्नुहोस्"""
    
    async def _format_context(self, inputs: Dict[str, Any]) -> str:
        """Format context documents for the prompt."""
        query = inputs.get("query", "")
        
        # Retrieve relevant documents
        documents = await self.retrieval_agent.retrieve_documents(query)
        
        if not documents:
            return "कुनै सान्दर्भिक दस्तावेज फेला परेन।"
        
        # Format documents
        context_parts = []
        for i, doc in enumerate(documents, 1):
            content = doc["content"][:1000]  # Limit content length
            metadata = doc.get("metadata", {})
            source = metadata.get("source", "अज्ञात स्रोत")
            
            context_parts.append(f"""
दस्तावेज {i}:
स्रोत: {source}
सामग्री: {content}
स्कोर: {doc.get('score', 0):.3f}
""")
        
        return "\n".join(context_parts)
    
    async def process_query(
        self, 
        query: str, 
        chat_history: List[Dict[str, Any]] = None,
        include_sources: bool = True
    ) -> Tuple[str, List[Dict[str, Any]]]:
        """Process a user query and return response with sources."""
        try:
            start_time = datetime.utcnow()
            
            # Prepare chat history
            if chat_history is None:
                chat_history = []
            
            # Convert chat history to LangChain format
            formatted_history = []
            for msg in chat_history[-10:]:  # Keep last 10 messages
                if msg.get("role") == "user":
                    formatted_history.append(HumanMessage(content=msg["content"]))
                elif msg.get("role") == "assistant":
                    formatted_history.append(AIMessage(content=msg["content"]))
            
            # Get documents for sources
            source_documents = []
            if include_sources:
                source_documents = await self.retrieval_agent.retrieve_documents(query)
            
            # Generate response
            response = await self.chain.ainvoke({
                "query": query,
                "chat_history": formatted_history
            })
            
            processing_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            logger.info(f"Generated response in {processing_time:.2f}ms")
            
            return response, source_documents
            
        except Exception as e:
            logger.error(f"Error processing query: {e}")
            fallback_response = "माफ गर्नुहोस्, तपाईंको प्रश्न प्रशोधन गर्न समस्या भयो। कृपया फेरि प्रयास गर्नुहोस्।"
            return fallback_response, []

class ConversationSummarizer:
    """
    Agent responsible for summarizing long conversations.
    Helps maintain context while keeping memory efficient.
    """
    
    def __init__(self):
        self.llm = ChatOpenAI(
            model="gpt-4o-mini",
            temperature=0.1,
            max_tokens=500
        )
        
        self.prompt = ChatPromptTemplate.from_messages([
            ("system", """तपाईंको काम भनेको कुराकानीको सारांश बनाउनु हो। 
            मुख्य बिन्दुहरू, छलफल गरिएका कानुनी विषयहरू, र महत्वपूर्ण जानकारीहरू समावेश गर्नुहोस्।
            नेपाली भाषामा संक्षिप्त र स्पष्ट सारांश प्रदान गर्नुहोस्।"""),
            ("human", "कुराकानी:\n{conversation}\n\nसारांश:")
        ])
        
        self.chain = self.prompt | self.llm | StrOutputParser()
    
    async def summarize_conversation(self, messages: List[Dict[str, Any]]) -> str:
        """Summarize a conversation."""
        try:
            # Format conversation
            conversation_text = []
            for msg in messages:
                role = "प्रयोगकर्ता" if msg["role"] == "user" else "सहायक"
                conversation_text.append(f"{role}: {msg['content']}")
            
            conversation = "\n".join(conversation_text)
            
            # Generate summary
            summary = await self.chain.ainvoke({"conversation": conversation})
            return summary

        except Exception as e:
            logger.error(f"Error summarizing conversation: {e}")
            return "कुराकानीको सारांश बनाउन समस्या भयो।"
