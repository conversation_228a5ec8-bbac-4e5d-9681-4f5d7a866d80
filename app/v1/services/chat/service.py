# app/v1/services/chat/service.py

import uuid
from typing import Dict, Any, <PERSON>, Optional, Tuple
from datetime import datetime

from langchain_core.messages import HumanMessage, AIMessage

from app.shared.database.models import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from .memory import MongoDBChatMemory
from .agents import LegalAssistantAgent, ConversationSummarizer
from .models import ChatResponse, SourceDocument, ChatMessage

logger = setup_new_logging(__name__)

class ChatService:
    """
    Modern chat service using LangChain with memory checkpoints and agents.
    
    Features:
    - LangChain MongoDB memory with checkpoints
    - Multi-agent system (main agent + document retrieval sub-agent)
    - Conversation summarization for long chats
    - Persistent session management
    """
    
    def __init__(self, current_user: UserTenantDB):
        self.current_user = current_user
        
        # Initialize components
        self.memory = MongoDBChatMemory(current_user)
        self.agent = LegalAssistantAgent(current_user)
        self.summarizer = ConversationSummarizer()
        
        logger.info(f"Initialized ChatService for user {current_user.id}")
    
    async def chat(
        self, 
        message: str, 
        session_id: Optional[str] = None,
        include_sources: bool = True,
        max_sources: int = 5
    ) -> ChatResponse:
        """
        Process a chat message and return response.
        
        Args:
            message: User message
            session_id: Optional session ID for conversation continuity
            include_sources: Whether to include source documents
            max_sources: Maximum number of source documents
            
        Returns:
            ChatResponse with assistant message and sources
        """
        try:
            start_time = datetime.utcnow()
            
            # Create or get session
            if not session_id:
                session_id = await self.memory.create_session()
            else:
                # Ensure session exists
                session_info = await self.memory.get_session_info(session_id)
                if not session_info:
                    session_id = await self.memory.create_session(session_id)
            
            # Get chat history
            chat_history = self.memory.get_chat_history(session_id)
            
            # Add user message to history
            user_message = HumanMessage(content=message)
            chat_history.add_message(user_message)
            
            # Convert history for agent processing
            history_messages = []
            for msg in chat_history.messages[-20:]:  # Last 20 messages for context
                history_messages.append({
                    "role": "user" if isinstance(msg, HumanMessage) else "assistant",
                    "content": msg.content
                })
            
            # Process with agent
            assistant_response, source_documents = await self.agent.process_query(
                query=message,
                chat_history=history_messages[:-1],  # Exclude the current message
                include_sources=include_sources
            )
            
            # Add assistant response to history
            ai_message = AIMessage(content=assistant_response)
            chat_history.add_message(ai_message)
            
            # Update session metadata
            await self.memory.update_session(session_id)
            
            # Check if we need to summarize (if conversation is getting long)
            if len(chat_history.messages) > 50:
                await self._maybe_summarize_conversation(session_id, chat_history)
            
            # Format source documents
            formatted_sources = []
            for doc in source_documents[:max_sources]:
                formatted_sources.append(SourceDocument(
                    id=doc.get("id", str(uuid.uuid4())),
                    content=doc.get("content", ""),
                    metadata=doc.get("metadata", {}),
                    score=doc.get("score", 0.0)
                ))
            
            # Calculate processing time
            processing_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            
            # Create response
            response = ChatResponse(
                message_id=str(uuid.uuid4()),
                user_message=message,
                assistant_message=assistant_response,
                sources=formatted_sources,
                session_id=session_id,
                timestamp=datetime.utcnow(),
                processing_time_ms=processing_time
            )
            
            logger.info(f"Processed chat message in {processing_time:.2f}ms")
            return response
            
        except Exception as e:
            logger.error(f"Error processing chat: {e}")
            # Return error response
            return ChatResponse(
                message_id=str(uuid.uuid4()),
                user_message=message,
                assistant_message="माफ गर्नुहोस्, तपाईंको सन्देश प्रशोधन गर्न समस्या भयो। कृपया फेरि प्रयास गर्नुहोस्।",
                sources=[],
                session_id=session_id or str(uuid.uuid4()),
                timestamp=datetime.utcnow(),
                processing_time_ms=0.0
            )
    
    async def get_chat_history(
        self, 
        session_id: str, 
        limit: int = 50
    ) -> List[ChatMessage]:
        """Get chat history for a session."""
        try:
            chat_history = self.memory.get_chat_history(session_id)
            
            # Convert to ChatMessage format
            messages = []
            for i, msg in enumerate(chat_history.messages[-limit:]):
                messages.append(ChatMessage(
                    id=f"{session_id}_{i}",
                    content=msg.content,
                    role="user" if isinstance(msg, HumanMessage) else "assistant",
                    timestamp=datetime.utcnow(),  # Note: LangChain doesn't store timestamps by default
                    metadata=getattr(msg, 'additional_kwargs', {})
                ))
            
            return messages
            
        except Exception as e:
            logger.error(f"Error getting chat history: {e}")
            return []
    
    async def list_sessions(self, limit: int = 20) -> List[Dict[str, Any]]:
        """List user's chat sessions."""
        try:
            sessions = await self.memory.list_user_sessions(limit)
            return sessions
        except Exception as e:
            logger.error(f"Error listing sessions: {e}")
            return []
    
    async def delete_session(self, session_id: str) -> bool:
        """Delete a chat session."""
        try:
            # Update session status to deleted
            await self.memory.sessions_collection.update_one(
                {"session_id": session_id, "user_id": self.current_user.id},
                {"$set": {"status": "deleted", "deleted_at": datetime.utcnow()}}
            )
            
            logger.info(f"Deleted session: {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting session: {e}")
            return False
    
    async def _maybe_summarize_conversation(self, session_id: str, chat_history):
        """Summarize conversation if it's getting too long."""
        try:
            # Get messages for summarization (exclude recent ones)
            messages_to_summarize = []
            for msg in chat_history.messages[:-20]:  # Keep last 20 messages
                messages_to_summarize.append({
                    "role": "user" if isinstance(msg, HumanMessage) else "assistant",
                    "content": msg.content
                })
            
            if len(messages_to_summarize) > 10:  # Only summarize if there are enough messages
                summary = await self.summarizer.summarize_conversation(messages_to_summarize)
                
                # Save checkpoint with summary
                checkpoint_data = {
                    "summary": summary,
                    "message_count": len(messages_to_summarize),
                    "summarized_at": datetime.utcnow().isoformat()
                }
                
                await self.memory.save_checkpoint(session_id, checkpoint_data)
                logger.info(f"Saved conversation checkpoint for session {session_id}")
                
        except Exception as e:
            logger.error(f"Error summarizing conversation: {e}")
    
    async def get_session_summary(self, session_id: str) -> Optional[str]:
        """Get the latest summary for a session."""
        try:
            checkpoint = await self.memory.get_latest_checkpoint(session_id)
            if checkpoint and "data" in checkpoint:
                return checkpoint["data"].get("summary")
            return None
        except Exception as e:
            logger.error(f"Error getting session summary: {e}")
            return None
