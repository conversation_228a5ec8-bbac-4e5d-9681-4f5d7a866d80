# app/v1/services/chat/simple_agent.py

from typing import List, Dict, Any, Optional, Annotated
from datetime import datetime, timezone
import uuid
import asyncio

from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_openai import ChatOpenAI
from langchain_core.tools import tool

from langgraph.graph import StateGraph, MessagesState, START, END
from langgraph.prebuilt import ToolNode
from langgraph.checkpoint.memory import MemorySaver

from app.shared.database.models import UserTenantDB
from app.shared.utils.logger import setup_new_logging
from app.v1.services.knowledge_base.service import KnowledgeBaseService

logger = setup_new_logging(__name__)

# LangMem imports for long-term memory
try:
    from langmem import Client as LangMemClient
    from langmem.storage import MongoDBStorage
    from langchain.storage import MongoDBStore
    from langmem.embeddings import OpenAIEmbeddings
    LANGMEM_AVAILABLE = True
except ImportError:
    LANGMEM_AVAILABLE = False
    logger.warning("LangMem not available, using basic memory")

class SimpleLegalAgent:
    """
    Simple LangGraph-based legal assistant agent with:
    - Document retrieval tool
    - Memory checkpoints
    - LSTM context handling
    """

    def __init__(self, current_user: UserTenantDB):
        self.current_user = current_user
        self.kb_service = KnowledgeBaseService(current_user)

        # Initialize LLM
        self.llm = ChatOpenAI(
            model="gpt-4o-mini",
            temperature=0.1,
            max_tokens=2000
        )

        # Create tools
        self.tools = self._create_tools()

        # Initialize LangMem for long-term memory
        self._init_langmem()

        # Create graph with memory
        self.memory = MemorySaver()
        self.graph = self._create_graph()

        logger.info(f"Initialized SimpleLegalAgent for user {current_user.id}")

    def _init_langmem(self):
        """Initialize LangMem for long-term memory."""
        if LANGMEM_AVAILABLE:
            try:
                # MongoDB connection for LangMem
                mongo_uri = getattr(self.current_user.env, 'MONGODB_URI', 'mongodb://localhost:27017')

                # Initialize storage
                storage = MongoDBStorage(
                    connection_string=mongo_uri,
                    database_name=self.current_user.adb.name,
                    collection_name="langmem_memories"
                )

                # Initialize embeddings
                embeddings = OpenAIEmbeddings()

                # Create LangMem client
                self.langmem_client = LangMemClient(
                    storage=storage,
                    embeddings=embeddings,
                    namespace=f"user_{self.current_user.id}"
                )

                self.use_langmem = True
                logger.info("LangMem initialized successfully")

            except Exception as e:
                logger.error(f"Error initializing LangMem: {e}")
                self.use_langmem = False
        else:
            self.use_langmem = False
            logger.info("LangMem not available, using basic memory")

    def _create_tools(self):
        """Create document retrieval tool."""
        
        @tool
        async def retrieve_documents(query: str, max_results: int = 5) -> List[Dict[str, Any]]:
            """
            Retrieve relevant legal documents based on the query.
            
            Args:
                query: The search query for finding relevant documents
                max_results: Maximum number of documents to retrieve (default: 5)
                
            Returns:
                List of relevant documents with content and metadata
            """
            try:
                logger.info(f"Retrieving documents for: {query[:100]}...")
                
                # Use knowledge base service for retrieval
                results = await self.kb_service.retrieve_source_nodes(query, max_results)
                
                # Format results
                formatted_results = []
                for result in results:
                    formatted_results.append({
                        "id": result.get("id", "unknown"),
                        "content": result.get("text", "")[:1000],  # Limit content length
                        "metadata": result.get("metadata", {}),
                        "score": result.get("score", 0.0),
                        "source": result.get("metadata", {}).get("source", "Unknown")
                    })
                
                logger.info(f"Retrieved {len(formatted_results)} documents")
                return formatted_results
                
            except Exception as e:
                logger.error(f"Error retrieving documents: {e}")
                return []
        
        return [retrieve_documents]
    
    def _create_graph(self):
        """Create the LangGraph workflow."""
        
        # Define the agent function
        def agent(state: MessagesState):
            """Main agent function that processes messages."""
            messages = state["messages"]
            
            # Add system message with context
            system_message = SystemMessage(content=self._get_system_prompt())
            
            # Bind tools to LLM
            llm_with_tools = self.llm.bind_tools(self.tools)
            
            # Get response from LLM
            response = llm_with_tools.invoke([system_message] + messages)
            
            return {"messages": [response]}
        
        # Define tool execution function
        def should_continue(state: MessagesState):
            """Determine if we should continue to tools or end."""
            messages = state["messages"]
            last_message = messages[-1]
            
            # If there are tool calls, continue to tools
            if hasattr(last_message, 'tool_calls') and last_message.tool_calls:
                return "tools"
            # Otherwise, end
            return END
        
        # Create the graph
        workflow = StateGraph(MessagesState)
        
        # Add nodes
        workflow.add_node("agent", agent)
        workflow.add_node("tools", ToolNode(self.tools))
        
        # Add edges
        workflow.add_edge(START, "agent")
        workflow.add_conditional_edges("agent", should_continue, {"tools": "tools", END: END})
        workflow.add_edge("tools", "agent")
        
        # Compile with memory
        return workflow.compile(checkpointer=self.memory)
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for the legal assistant."""
        return """तपाईं एक नेपाली कानुनी सहायक हुनुहुन्छ। तपाईंको काम भनेको:

1. नेपाली कानुनी प्रश्नहरूको सटीक र उपयोगी जवाफ दिनु
2. retrieve_documents tool प्रयोग गरेर सान्दर्भिक दस्तावेजहरू खोज्नु
3. कानुनी सल्लाह नभएर सामान्य जानकारी मात्र प्रदान गर्नु
4. नेपाली भाषामा स्पष्ट र बुझ्न सकिने भाषामा जवाफ दिनु

महत्वपूर्ण निर्देशनहरू:
- सधैं retrieve_documents tool प्रयोग गरेर सान्दर्भिक दस्तावेजहरू खोज्नुहोस्
- दस्तावेजहरूको आधारमा जवाफ दिनुहोस्
- यदि जानकारी उपलब्ध छैन भने स्पष्ट रूपमा भन्नुहोस्
- कानुनी सल्लाहको लागि योग्य वकिलसँग सम्पर्क गर्न सुझाव दिनुहोस्
- तथ्यपरक र निष्पक्ष जानकारी प्रदान गर्नुहोस्

सधैं पहिले retrieve_documents tool प्रयोग गर्नुहोस्, त्यसपछि मात्र जवाफ दिनुहोस्।"""

    def _get_context_aware_prompt(self, context_summary: Dict[str, Any], context_info: Dict[str, Any]) -> str:
        """Get context-aware system prompt with LSTM insights."""
        base_prompt = self._get_system_prompt()

        # Add context information
        context_addition = f"""

कुराकानी सन्दर्भ जानकारी:
- सन्दर्भ स्थिति: {context_summary.get('summary', 'उपलब्ध छैन')}
- सान्दर्भिकता स्कोर: {context_info.get('relevance_score', 0):.2f}
- सन्देश लम्बाइ: {context_info.get('message_length', 0)} शब्दहरू

यो सन्दर्भ जानकारीको आधारमा अझ राम्रो र सान्दर्भिक जवाफ दिनुहोस्।"""

        return base_prompt + context_addition
    
    async def process_query(
        self, 
        query: str, 
        session_id: str,
        chat_history: List[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Process a user query using the LangGraph agent.
        
        Args:
            query: User query
            session_id: Session ID for memory
            chat_history: Previous conversation history
            
        Returns:
            Response with content and sources
        """
        try:
            start_time = datetime.now(timezone.utc)

            # Get relevant context from LangMem
            relevant_context = await self._get_relevant_context(query)

            # Prepare messages
            messages = []

            # Add context-aware system message
            context_prompt = self._get_context_aware_prompt(relevant_context)
            messages.append(SystemMessage(content=context_prompt))

            # Add chat history if provided
            if chat_history:
                for msg in chat_history[-10:]:  # Keep last 10 messages
                    if msg.get("role") == "user":
                        messages.append(HumanMessage(content=msg["content"]))
                    elif msg.get("role") == "assistant":
                        messages.append(AIMessage(content=msg["content"]))

            # Add current query
            messages.append(HumanMessage(content=query))
            
            # Create thread config for memory
            config = {"configurable": {"thread_id": session_id}}
            
            # Process with the graph
            result = await self.graph.ainvoke(
                {"messages": messages},
                config=config
            )
            
            # Extract response and sources
            response_messages = result["messages"]
            assistant_message = response_messages[-1]

            # Extract sources from tool calls if any
            sources = []
            for msg in response_messages:
                if hasattr(msg, 'tool_calls') and msg.tool_calls:
                    for tool_call in msg.tool_calls:
                        if tool_call["name"] == "retrieve_documents":
                            # The tool result should be in the next message
                            continue
                elif hasattr(msg, 'content') and isinstance(msg.content, list):
                    # Check for tool results
                    for content_item in msg.content:
                        if hasattr(content_item, 'content') and isinstance(content_item.content, list):
                            sources.extend(content_item.content)

            # Store conversation in LangMem for future context
            await self._store_conversation_memory(session_id, query, assistant_message.content)

            processing_time = (datetime.now(timezone.utc) - start_time).total_seconds() * 1000
            
            return {
                "content": assistant_message.content,
                "sources": sources,
                "processing_time_ms": processing_time,
                "session_id": session_id
            }
            
        except Exception as e:
            logger.error(f"Error processing query: {e}")
            return {
                "content": "माफ गर्नुहोस्, तपाईंको प्रश्न प्रशोधन गर्न समस्या भयो। कृपया फेरि प्रयास गर्नुहोस्।",
                "sources": [],
                "processing_time_ms": 0.0,
                "session_id": session_id
            }
    
    async def get_memory_summary(self, session_id: str) -> Optional[str]:
        """Get memory summary for a session."""
        try:
            config = {"configurable": {"thread_id": session_id}}
            
            # Get the current state
            state = await self.graph.aget_state(config)
            
            if state and state.values.get("messages"):
                messages = state.values["messages"]
                if len(messages) > 5:
                    return f"कुराकानीमा {len(messages)} सन्देशहरू छन्। पछिल्लो छलफल: {messages[-1].content[:100]}..."
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting memory summary: {e}")
            return None
