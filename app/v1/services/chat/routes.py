# app/v1/services/chat/routes.py

from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional

from app.shared.database.models import UserTenantDB
from app.shared.security.dependencies import get_current_user
from app.shared.utils.logger import setup_new_logging

from .service import ChatService
from .models import ChatRequest, ChatResponse, ChatMessage

router = APIRouter(prefix="/chat", tags=["Chat"])
logger = setup_new_logging(__name__)

@router.post("/", response_model=ChatResponse)
async def chat(
    request: ChatRequest,
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Send a message and get AI response.
    
    This endpoint handles:
    - User message processing
    - AI response generation using LangChain agents
    - Document retrieval and source citation
    - Conversation memory management
    - Session continuity
    """
    try:
        service = ChatService(current_user)
        
        response = await service.chat(
            message=request.message,
            session_id=request.session_id,
            include_sources=request.include_sources,
            max_sources=request.max_sources
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Error in chat endpoint: {e}")
        raise HTTPException(status_code=500, detail="Failed to process chat message")

@router.get("/history/{session_id}", response_model=List[ChatMessage])
async def get_chat_history(
    session_id: str,
    limit: int = Query(50, ge=1, le=200, description="Number of messages to retrieve"),
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Get chat history for a specific session.
    
    Returns the conversation history with proper message ordering
    and metadata including timestamps and source references.
    """
    try:
        service = ChatService(current_user)
        messages = await service.get_chat_history(session_id, limit)
        return messages
        
    except Exception as e:
        logger.error(f"Error getting chat history: {e}")
        raise HTTPException(status_code=500, detail="Failed to retrieve chat history")

@router.get("/sessions")
async def list_sessions(
    limit: int = Query(20, ge=1, le=100, description="Number of sessions to retrieve"),
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    List user's chat sessions.
    
    Returns active chat sessions with metadata including
    creation time, last activity, and message count.
    """
    try:
        service = ChatService(current_user)
        sessions = await service.list_sessions(limit)
        return {"sessions": sessions, "count": len(sessions)}
        
    except Exception as e:
        logger.error(f"Error listing sessions: {e}")
        raise HTTPException(status_code=500, detail="Failed to list sessions")

@router.delete("/sessions/{session_id}")
async def delete_session(
    session_id: str,
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Delete a chat session.
    
    Marks the session as deleted and removes it from active sessions.
    The actual message history is preserved for audit purposes.
    """
    try:
        service = ChatService(current_user)
        success = await service.delete_session(session_id)
        
        if success:
            return {"message": "Session deleted successfully", "session_id": session_id}
        else:
            raise HTTPException(status_code=404, detail="Session not found")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting session: {e}")
        raise HTTPException(status_code=500, detail="Failed to delete session")

@router.get("/sessions/{session_id}/summary")
async def get_session_summary(
    session_id: str,
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Get conversation summary for a session.
    
    Returns an AI-generated summary of the conversation,
    useful for long chats or session overview.
    """
    try:
        service = ChatService(current_user)
        summary = await service.get_session_summary(session_id)
        
        if summary:
            return {"session_id": session_id, "summary": summary}
        else:
            return {"session_id": session_id, "summary": None, "message": "No summary available"}
            
    except Exception as e:
        logger.error(f"Error getting session summary: {e}")
        raise HTTPException(status_code=500, detail="Failed to get session summary")

# Simple endpoint for quick testing
@router.post("/simple")
async def simple_chat(
    message: str,
    session_id: Optional[str] = None,
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Simple chat endpoint for quick testing.
    
    Accepts a plain message string and returns a simple response.
    Useful for frontend integration and testing.
    """
    try:
        service = ChatService(current_user)
        
        response = await service.chat(
            message=message,
            session_id=session_id,
            include_sources=True,
            max_sources=5
        )
        
        return {
            "user_message": response.user_message,
            "assistant_message": response.assistant_message,
            "sources_count": len(response.sources),
            "session_id": response.session_id,
            "processing_time_ms": response.processing_time_ms
        }
        
    except Exception as e:
        logger.error(f"Error in simple chat: {e}")
        raise HTTPException(status_code=500, detail="Failed to process message")
