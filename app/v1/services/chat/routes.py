# app/v1/services/chat/routes.py

from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional

from app.shared.database.models import UserTenantDB
from app.shared.security.dependencies import get_current_user
from app.shared.utils.logger import setup_new_logging
from .service import ChatService
from .models import ChatQuery, ChatResponse, ChatHistory, ConversationSummary

router = APIRouter(prefix="/chat", tags=["Chat"])
logger = setup_new_logging(__name__)

@router.post("/query", response_model=ChatResponse)
async def chat_query(
    query_data: ChatQuery,
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Process a chat query and return AI-generated response with source references.
    """
    return await ChatService.process_chat_query(
        query=query_data.query,
        current_user=current_user,
        max_results=query_data.max_results,
        conversation_id=query_data.conversation_id,
        include_sources=query_data.include_sources
    )

@router.get("/history", response_model=List[ChatHistory])
async def get_chat_history(
    conversation_id: Optional[str] = Query(None, description="Filter by conversation ID"),
    limit: int = Query(50, ge=1, le=100, description="Number of messages to retrieve"),
    skip: int = Query(0, ge=0, description="Number of messages to skip"),
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Get chat history for the current user.
    """
    return await ChatService.get_chat_history(
        current_user=current_user,
        conversation_id=conversation_id,
        limit=limit,
        skip=skip
    )

@router.get("/conversations", response_model=List[ConversationSummary])
async def get_conversations(
    limit: int = Query(20, ge=1, le=50, description="Number of conversations to retrieve"),
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Get conversation summaries for the current user.
    """
    return await ChatService.get_conversations(
        current_user=current_user,
        limit=limit
    )

@router.get("/search")
async def search_documents(
    query: str = Query(..., description="Search query"),
    max_results: int = Query(5, ge=1, le=20, description="Maximum number of results"),
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Search for relevant document chunks without AI response generation.
    Useful for getting raw search results.
    """
    from app.v1.services.knowledge_base.service import KnowledgeBaseService
    
    # Get grouped search results
    results = await KnowledgeBaseService.search_documents(query, current_user, max_results)
    
    return {
        "query": query,
        "results": results,
        "total_found": len(results)
    }

@router.delete("/conversation/{conversation_id}")
async def delete_conversation(
    conversation_id: str,
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Delete a specific conversation and all its messages.
    """
    try:
        chat_collection = current_user.adb.chat_history
        
        # Delete all messages in the conversation
        result = await chat_collection.delete_many({
            "conversation_id": conversation_id,
            "user_id": current_user.id,
            "tenant_id": current_user.tenant_id
        })
        
        if result.deleted_count == 0:
            raise HTTPException(status_code=404, detail="Conversation not found")
        
        logger.info(f"Deleted conversation {conversation_id} with {result.deleted_count} messages")
        
        return {
            "message": f"Conversation deleted successfully",
            "conversation_id": conversation_id,
            "messages_deleted": result.deleted_count
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting conversation: {e}")
        raise HTTPException(status_code=500, detail="Error deleting conversation")

@router.get("/attribution/{document_id}")
async def get_source_attribution_map(
    document_id: str,
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Get comprehensive source attribution map for a document.
    Maps all chunks to their exact locations in the PDF.
    """
    return await ChatService.create_source_attribution_map(document_id, current_user)

@router.get("/chunk/{chunk_id}/location")
async def get_chunk_location(
    chunk_id: str,
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Get the exact location of a specific chunk in its source document.
    """
    return await ChatService.get_chunk_location(chunk_id, current_user)

@router.get("/health")
async def chat_health_check():
    """
    Health check endpoint for the chat service.
    """
    return {
        "status": "healthy",
        "service": "chat",
        "version": "1.0.0"
    }
