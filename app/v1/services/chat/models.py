# app/v1/services/chat/models.py

from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from datetime import datetime

class ChatMessage(BaseModel):
    """Chat message model."""
    id: str
    content: str
    role: str  # 'user' or 'assistant'
    timestamp: datetime
    metadata: Optional[Dict[str, Any]] = None

class ChatRequest(BaseModel):
    """Chat request model."""
    message: str = Field(..., description="User message")
    session_id: Optional[str] = Field(None, description="Session ID for conversation continuity")
    include_sources: bool = Field(True, description="Whether to include source documents")
    max_sources: int = Field(5, ge=1, le=20, description="Maximum number of source documents")

class SourceDocument(BaseModel):
    """Source document model."""
    id: str
    content: str
    metadata: Dict[str, Any]
    score: float

class ChatResponse(BaseModel):
    """Chat response model."""
    message_id: str
    user_message: str
    assistant_message: str
    sources: List[SourceDocument] = Field(default_factory=list)
    session_id: str
    timestamp: datetime
    processing_time_ms: float

class ChatHistory(BaseModel):
    """Chat history model."""
    session_id: str
    messages: List[ChatMessage]
    total_messages: int
    created_at: datetime
    updated_at: datetime
