# app/v1/services/chat/memory.py

import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime
from pymongo import MongoClient
from pymongo.collection import Collection

from langchain_core.chat_history import BaseChatMessageHistory
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from langchain_mongodb import MongoDBChatMessageHistory

from app.shared.database.models import UserTenantDB
from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)

class MongoDBChatMemory:
    """
    MongoDB-based chat memory using LangChain's MongoDBChatMessageHistory.
    Provides persistent conversation memory with checkpoints.
    """
    
    def __init__(self, current_user: UserTenantDB):
        self.current_user = current_user
        self.db = current_user.adb
        
        # MongoDB connection for LangChain
        self.mongo_uri = getattr(current_user.env, 'MONGODB_URI', 'mongodb://localhost:27017')
        self.database_name = self.db.name
        
        # Collections for metadata
        self.sessions_collection: Collection = self.db.chat_sessions
        self.checkpoints_collection: Collection = self.db.chat_checkpoints
        
        # Create indexes
        self._create_indexes()
    
    def _create_indexes(self):
        """Create necessary indexes."""
        try:
            # Session indexes
            self.sessions_collection.create_index([("user_id", 1), ("session_id", 1)], unique=True)
            self.sessions_collection.create_index([("user_id", 1), ("updated_at", -1)])
            
            # Checkpoint indexes
            self.checkpoints_collection.create_index([("session_id", 1), ("checkpoint_id", 1)], unique=True)
            self.checkpoints_collection.create_index([("session_id", 1), ("created_at", -1)])
            
            logger.debug("Created chat memory indexes")
        except Exception as e:
            logger.warning(f"Error creating indexes: {e}")
    
    def get_chat_history(self, session_id: str) -> MongoDBChatMessageHistory:
        """Get LangChain chat message history for a session."""
        try:
            return MongoDBChatMessageHistory(
                connection_string=self.mongo_uri,
                session_id=f"{self.current_user.id}_{session_id}",
                database_name=self.database_name,
                collection_name="chat_message_history"
            )
        except Exception as e:
            logger.error(f"Error creating chat history: {e}")
            raise
    
    async def create_session(self, session_id: Optional[str] = None) -> str:
        """Create a new chat session."""
        if not session_id:
            session_id = str(uuid.uuid4())
        
        try:
            session_doc = {
                "session_id": session_id,
                "user_id": self.current_user.id,
                "tenant_id": self.current_user.tenant_id,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow(),
                "message_count": 0,
                "status": "active"
            }
            
            await self.sessions_collection.insert_one(session_doc)
            logger.info(f"Created chat session: {session_id}")
            return session_id
            
        except Exception as e:
            logger.error(f"Error creating session: {e}")
            raise
    
    async def update_session(self, session_id: str):
        """Update session metadata."""
        try:
            await self.sessions_collection.update_one(
                {"session_id": session_id, "user_id": self.current_user.id},
                {
                    "$set": {"updated_at": datetime.utcnow()},
                    "$inc": {"message_count": 1}
                }
            )
        except Exception as e:
            logger.error(f"Error updating session: {e}")
    
    async def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session information."""
        try:
            session = await self.sessions_collection.find_one({
                "session_id": session_id,
                "user_id": self.current_user.id
            })
            return session
        except Exception as e:
            logger.error(f"Error getting session info: {e}")
            return None
    
    async def list_user_sessions(self, limit: int = 20) -> List[Dict[str, Any]]:
        """List user's chat sessions."""
        try:
            cursor = self.sessions_collection.find({
                "user_id": self.current_user.id,
                "status": "active"
            }).sort("updated_at", -1).limit(limit)
            
            sessions = await cursor.to_list(length=limit)
            return sessions
        except Exception as e:
            logger.error(f"Error listing sessions: {e}")
            return []
    
    async def save_checkpoint(self, session_id: str, checkpoint_data: Dict[str, Any]):
        """Save a conversation checkpoint."""
        try:
            checkpoint_doc = {
                "checkpoint_id": str(uuid.uuid4()),
                "session_id": session_id,
                "user_id": self.current_user.id,
                "data": checkpoint_data,
                "created_at": datetime.utcnow()
            }
            
            await self.checkpoints_collection.insert_one(checkpoint_doc)
            
        except Exception as e:
            logger.error(f"Error saving checkpoint: {e}")
    
    async def get_latest_checkpoint(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get the latest checkpoint for a session."""
        try:
            checkpoint = await self.checkpoints_collection.find_one(
                {"session_id": session_id, "user_id": self.current_user.id},
                sort=[("created_at", -1)]
            )
            return checkpoint
        except Exception as e:
            logger.error(f"Error getting checkpoint: {e}")
            return None
