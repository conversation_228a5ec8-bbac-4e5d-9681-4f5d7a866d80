# app/v1/services/agents/response_synthesis.py

import time
from typing import Dict, Any, List, Optional
from collections import defaultdict
from langchain_openai import ChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage

from app.shared.config.agent_config import agent_config
from app.shared.utils.logger import setup_new_logging
from app.v1.services.chat.models import UniformResponse, SourceGroup, SentenceData
from .types import SourceReference

logger = setup_new_logging(__name__)

class ResponseSynthesisEngine:
    """
    LLM-powered response synthesis engine that combines tool outputs,
    formats source references, and generates comprehensive answers.
    
    Handles source node grouping post-agent execution as requested.
    """
    
    def __init__(self):
        self.llm = ChatOpenAI(
            model=agent_config.AGENT_LLM_MODEL,
            temperature=agent_config.AGENT_LLM_TEMPERATURE,
            max_tokens=agent_config.AGENT_LLM_MAX_TOKENS
        )
        logger.info("Initialized ResponseSynthesisEngine")
    
    async def synthesize_response(
        self,
        user_query: str,
        tool_results: List[Dict[str, Any]],
        conversation_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Synthesize a comprehensive response from tool outputs.
        
        Args:
            user_query: The original user query
            tool_results: List of results from executed tools
            conversation_context: Optional conversation context
            
        Returns:
            Dictionary containing synthesized response with grouped sources
        """
        try:
            # Extract and group sources from tool results
            grouped_sources = self._group_sources_by_document(tool_results)
            
            # Create uniform response format
            uniform_sources = self._create_uniform_response(grouped_sources)
            
            # Generate synthesized content using LLM
            synthesized_content = await self._generate_synthesized_content(
                user_query, tool_results, grouped_sources, conversation_context
            )
            
            # Collect all legacy sources for backward compatibility
            all_sources = []
            for result in tool_results:
                if result.get("sources"):
                    all_sources.extend(result["sources"])
            
            return {
                "content": synthesized_content,
                "uniform_sources": uniform_sources,
                "sources": all_sources,  # Legacy format
                "synthesis_metadata": {
                    "tools_used": len(tool_results),
                    "total_sources": len(all_sources),
                    "grouped_sources": len(grouped_sources),
                    "synthesis_method": "llm_powered"
                }
            }
            
        except Exception as e:
            logger.error(f"Error in response synthesis: {e}")
            return self._create_fallback_response(user_query, tool_results)
    
    def _group_sources_by_document(self, tool_results: List[Dict[str, Any]]) -> Dict[str, List[SourceReference]]:
        """Group source references by document/PDF source."""
        grouped = defaultdict(list)
        
        for result in tool_results:
            sources = result.get("sources", [])
            for source in sources:
                if isinstance(source, SourceReference):
                    document_key = source.filename or "unknown.pdf"
                    grouped[document_key].append(source)
                elif isinstance(source, dict):
                    # Handle dict format
                    document_key = source.get("filename", "unknown.pdf")
                    grouped[document_key].append(source)
        
        return dict(grouped)
    
    def _create_uniform_response(self, grouped_sources: Dict[str, List[SourceReference]]) -> UniformResponse:
        """Create uniform response format with grouped sources."""
        source_groups = []
        total_sources = 0
        
        for document_name, sources in grouped_sources.items():
            sentences = []
            
            for source in sources:
                if isinstance(source, SourceReference):
                    sentence_data = SentenceData(
                        sentence=source.text_snippet,
                        page_number=source.page_number or 0,
                        sent_id=source.document_id or source.chunk_id or "",
                        confidence_score=source.confidence_score
                    )
                elif isinstance(source, dict):
                    sentence_data = SentenceData(
                        sentence=source.get("text_snippet", ""),
                        page_number=source.get("page_number", 0),
                        sent_id=source.get("document_id", "") or source.get("chunk_id", ""),
                        confidence_score=source.get("confidence_score", 0.0)
                    )
                else:
                    continue
                
                sentences.append(sentence_data)
                total_sources += 1
            
            if sentences:
                source_group = SourceGroup(
                    source=document_name,
                    sentences=sentences
                )
                source_groups.append(source_group)
        
        return UniformResponse(
            sources=source_groups,
            total_sources_found=total_sources
        )
    
    async def _generate_synthesized_content(
        self,
        user_query: str,
        tool_results: List[Dict[str, Any]],
        grouped_sources: Dict[str, List[SourceReference]],
        conversation_context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Generate synthesized content using LLM."""
        try:
            # Create synthesis prompt
            synthesis_prompt = self._create_synthesis_prompt(
                user_query, tool_results, grouped_sources, conversation_context
            )
            
            # Get LLM response
            response = await self.llm.ainvoke([
                SystemMessage(content=self._get_synthesis_system_prompt()),
                HumanMessage(content=synthesis_prompt)
            ])
            
            return response.content
            
        except Exception as e:
            logger.error(f"Error generating synthesized content: {e}")
            return self._create_fallback_content(user_query, tool_results)
    
    def _get_synthesis_system_prompt(self) -> str:
        """Get the system prompt for response synthesis."""
        return """You are a legal assistant that synthesizes comprehensive responses from multiple tool outputs.

Your responsibilities:
1. Combine information from all tool results into a coherent response
2. Always respond in Nepali unless specifically requested otherwise
3. Reference sources appropriately when using document search results
4. Maintain a helpful and professional tone
5. If there were errors in tool execution, acknowledge limitations but provide available information
6. Structure the response clearly with proper formatting
7. Ensure accuracy and relevance to the user's query

Guidelines for source references:
- When referencing documents, mention the document name and page number
- Use phrases like "अनुसार" (according to) when citing sources
- Group related information from the same document together
- Provide specific page references when available
"""
    
    def _create_synthesis_prompt(
        self,
        user_query: str,
        tool_results: List[Dict[str, Any]],
        grouped_sources: Dict[str, List[SourceReference]],
        conversation_context: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create the synthesis prompt for the LLM."""
        prompt_parts = [
            f"User Query: {user_query}",
            "",
            "Tool Results:"
        ]
        
        # Add tool results
        for i, result in enumerate(tool_results, 1):
            tool_name = result.get("tool_name", "unknown")
            content = result.get("content", "")
            error = result.get("error")
            
            if error:
                prompt_parts.append(f"{i}. {tool_name}: Error - {error}")
            else:
                prompt_parts.append(f"{i}. {tool_name}: {content}")
        
        # Add source information if available
        if grouped_sources:
            prompt_parts.extend([
                "",
                "Available Sources by Document:"
            ])
            
            for doc_name, sources in grouped_sources.items():
                prompt_parts.append(f"- {doc_name}: {len(sources)} relevant sections")
                for source in sources[:3]:  # Show first 3 sources per document
                    if isinstance(source, SourceReference):
                        page_info = f" (Page {source.page_number})" if source.page_number else ""
                        snippet = source.text_snippet[:100] + "..." if len(source.text_snippet) > 100 else source.text_snippet
                        prompt_parts.append(f"  * {snippet}{page_info}")
        
        # Add conversation context if available
        if conversation_context and conversation_context.get("history"):
            prompt_parts.extend([
                "",
                "Recent Conversation Context:",
                f"Previous interactions: {len(conversation_context['history'])}"
            ])
        
        prompt_parts.extend([
            "",
            "Instructions:",
            "1. Synthesize a comprehensive response in Nepali that directly addresses the user's query",
            "2. Combine information from all successful tool results",
            "3. Reference specific documents and page numbers when using search results",
            "4. Structure the response clearly and professionally",
            "5. If multiple tools were used, integrate their outputs coherently",
            "",
            "Synthesized Response:"
        ])
        
        return "\n".join(prompt_parts)
    
    def _create_fallback_response(self, user_query: str, tool_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Create a fallback response when synthesis fails."""
        # Combine tool results directly
        combined_content = []
        all_sources = []
        
        for result in tool_results:
            if result.get("content") and not result.get("error"):
                combined_content.append(result["content"])
            if result.get("sources"):
                all_sources.extend(result["sources"])
        
        content = "\n\n".join(combined_content) if combined_content else "माफ गर्नुहोस्, प्रतिक्रिया उत्पादन गर्न सकिएन।"
        
        return {
            "content": content,
            "uniform_sources": UniformResponse(sources=[], total_sources_found=len(all_sources)),
            "sources": all_sources,
            "synthesis_metadata": {
                "tools_used": len(tool_results),
                "total_sources": len(all_sources),
                "synthesis_method": "fallback"
            }
        }
    
    def _create_fallback_content(self, user_query: str, tool_results: List[Dict[str, Any]]) -> str:
        """Create fallback content when LLM synthesis fails."""
        successful_results = [r for r in tool_results if r.get("content") and not r.get("error")]
        
        if successful_results:
            contents = [r["content"] for r in successful_results]
            return f"तपाईंको प्रश्न \"{user_query}\" को जवाफमा:\n\n" + "\n\n".join(contents)
        else:
            return f"माफ गर्नुहोस्, तपाईंको प्रश्न \"{user_query}\" को लागि उपयुक्त जानकारी फेला पार्न सकिएन।"
