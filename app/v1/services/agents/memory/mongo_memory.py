# app/v1/services/agents/memory/mongo_memory.py

import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Iterator, Tuple
from pymongo import MongoClient
from pymongo.collection import Collection
from pymongo.database import Database

from langgraph.checkpoint.base import BaseCheckpointSaver, Checkpoint, CheckpointMetadata
from langgraph.checkpoint.base import CheckpointTuple
from langgraph.store.base import BaseStore

from app.shared.config.agent_config import agent_config
from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)

class MongoDBCheckpointer(BaseCheckpointSaver):
    """
    MongoDB-based checkpointer for LangGraph persistent memory.
    
    Stores conversation checkpoints in MongoDB for:
    - Session persistence across restarts
    - Conversation history
    - State recovery
    - Multi-tenant isolation
    """
    
    def __init__(self, mongo_uri: Optional[str] = None, db_name: Optional[str] = None):
        self.mongo_uri = mongo_uri or agent_config.memory_mongo_uri
        self.db_name = db_name or agent_config.AGENT_MEMORY_DB_NAME
        self.collection_name = agent_config.AGENT_CHECKPOINTS_COLLECTION
        
        self.client = MongoClient(self.mongo_uri)
        self.db: Database = self.client[self.db_name]
        self.collection: Collection = self.db[self.collection_name]
        
        # Create indexes for performance
        self._create_indexes()
        
        logger.info(f"Initialized MongoDB checkpointer: {self.db_name}.{self.collection_name}")
    
    def _create_indexes(self):
        """Create necessary indexes for efficient querying."""
        try:
            # Index on thread_id for fast thread-based queries
            self.collection.create_index("thread_id")
            
            # Index on thread_id + checkpoint_id for specific checkpoint retrieval
            self.collection.create_index([("thread_id", 1), ("checkpoint_id", 1)])
            
            # Index on created_at for time-based queries
            self.collection.create_index("created_at")
            
            # Index on tenant_id for multi-tenant isolation
            self.collection.create_index("tenant_id")
            
            logger.info("Created MongoDB indexes for checkpointer")
        except Exception as e:
            logger.warning(f"Failed to create indexes: {e}")
    
    def put(
        self,
        config: Dict[str, Any],
        checkpoint: Checkpoint,
        metadata: CheckpointMetadata,
        new_versions: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Save a checkpoint to MongoDB."""
        try:
            thread_id = config["configurable"]["thread_id"]
            checkpoint_id = checkpoint["id"]
            tenant_id = config["configurable"].get("tenant_id", "default")
            
            document = {
                "thread_id": thread_id,
                "checkpoint_id": checkpoint_id,
                "tenant_id": tenant_id,
                "checkpoint": checkpoint,
                "metadata": metadata,
                "new_versions": new_versions,
                "created_at": datetime.utcnow(),
                "config": config
            }
            
            # Upsert the document
            self.collection.replace_one(
                {"thread_id": thread_id, "checkpoint_id": checkpoint_id},
                document,
                upsert=True
            )
            
            logger.debug(f"Saved checkpoint {checkpoint_id} for thread {thread_id}")
            return config
            
        except Exception as e:
            logger.error(f"Failed to save checkpoint: {e}")
            raise
    
    def get_tuple(self, config: Dict[str, Any]) -> Optional[CheckpointTuple]:
        """Retrieve a specific checkpoint from MongoDB."""
        try:
            thread_id = config["configurable"]["thread_id"]
            checkpoint_id = config["configurable"].get("checkpoint_id")
            tenant_id = config["configurable"].get("tenant_id", "default")
            
            query = {"thread_id": thread_id, "tenant_id": tenant_id}
            if checkpoint_id:
                query["checkpoint_id"] = checkpoint_id
            
            # Get the most recent checkpoint if no specific ID provided
            document = self.collection.find_one(
                query,
                sort=[("created_at", -1)]
            )
            
            if not document:
                return None
            
            return CheckpointTuple(
                config=document["config"],
                checkpoint=document["checkpoint"],
                metadata=document["metadata"],
                parent_config=document.get("parent_config")
            )
            
        except Exception as e:
            logger.error(f"Failed to retrieve checkpoint: {e}")
            return None
    
    def list(
        self,
        config: Dict[str, Any],
        *,
        filter: Optional[Dict[str, Any]] = None,
        before: Optional[Dict[str, Any]] = None,
        limit: Optional[int] = None
    ) -> Iterator[CheckpointTuple]:
        """List checkpoints for a thread."""
        try:
            thread_id = config["configurable"]["thread_id"]
            tenant_id = config["configurable"].get("tenant_id", "default")
            
            query = {"thread_id": thread_id, "tenant_id": tenant_id}
            
            if filter:
                query.update(filter)
            
            if before:
                query["created_at"] = {"$lt": before.get("created_at", datetime.utcnow())}
            
            cursor = self.collection.find(query).sort("created_at", -1)
            
            if limit:
                cursor = cursor.limit(limit)
            
            for document in cursor:
                yield CheckpointTuple(
                    config=document["config"],
                    checkpoint=document["checkpoint"],
                    metadata=document["metadata"],
                    parent_config=document.get("parent_config")
                )
                
        except Exception as e:
            logger.error(f"Failed to list checkpoints: {e}")
    
    def delete_thread(self, thread_id: str, tenant_id: str = "default") -> None:
        """Delete all checkpoints for a thread."""
        try:
            result = self.collection.delete_many({
                "thread_id": thread_id,
                "tenant_id": tenant_id
            })
            logger.info(f"Deleted {result.deleted_count} checkpoints for thread {thread_id}")
        except Exception as e:
            logger.error(f"Failed to delete thread {thread_id}: {e}")
            raise

class MongoMemoryStore(BaseStore):
    """
    MongoDB-based store for long-term memory and user context.
    
    Stores:
    - User preferences and settings
    - Conversation summaries
    - Agent-specific memory
    - Cross-session context
    """
    
    def __init__(self, mongo_uri: Optional[str] = None, db_name: Optional[str] = None):
        self.mongo_uri = mongo_uri or agent_config.memory_mongo_uri
        self.db_name = db_name or agent_config.AGENT_MEMORY_DB_NAME
        self.collection_name = agent_config.AGENT_USER_CONTEXT_COLLECTION
        
        self.client = MongoClient(self.mongo_uri)
        self.db: Database = self.client[self.db_name]
        self.collection: Collection = self.db[self.collection_name]
        
        # Create indexes
        self._create_indexes()
        
        logger.info(f"Initialized MongoDB memory store: {self.db_name}.{self.collection_name}")
    
    def _create_indexes(self):
        """Create necessary indexes."""
        try:
            # Compound index on namespace and key
            self.collection.create_index([("namespace", 1), ("key", 1)], unique=True)
            
            # Index on tenant_id for multi-tenant isolation
            self.collection.create_index("tenant_id")
            
            # Index on created_at for time-based queries
            self.collection.create_index("created_at")
            
            logger.info("Created MongoDB indexes for memory store")
        except Exception as e:
            logger.warning(f"Failed to create memory store indexes: {e}")
    
    def put(
        self,
        namespace: Tuple[str, ...],
        key: str,
        value: Dict[str, Any],
        tenant_id: str = "default"
    ) -> None:
        """Store a value in the memory store."""
        try:
            document = {
                "namespace": list(namespace),
                "key": key,
                "value": value,
                "tenant_id": tenant_id,
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }
            
            self.collection.replace_one(
                {"namespace": list(namespace), "key": key, "tenant_id": tenant_id},
                document,
                upsert=True
            )
            
            logger.debug(f"Stored value for {namespace}/{key}")
            
        except Exception as e:
            logger.error(f"Failed to store value: {e}")
            raise
    
    def get(
        self,
        namespace: Tuple[str, ...],
        key: str,
        tenant_id: str = "default"
    ) -> Optional[Dict[str, Any]]:
        """Retrieve a value from the memory store."""
        try:
            document = self.collection.find_one({
                "namespace": list(namespace),
                "key": key,
                "tenant_id": tenant_id
            })
            
            return document["value"] if document else None
            
        except Exception as e:
            logger.error(f"Failed to retrieve value: {e}")
            return None
    
    def delete(
        self,
        namespace: Tuple[str, ...],
        key: str,
        tenant_id: str = "default"
    ) -> bool:
        """Delete a value from the memory store."""
        try:
            result = self.collection.delete_one({
                "namespace": list(namespace),
                "key": key,
                "tenant_id": tenant_id
            })
            
            return result.deleted_count > 0
            
        except Exception as e:
            logger.error(f"Failed to delete value: {e}")
            return False
    
    def list_namespace(
        self,
        namespace: Tuple[str, ...],
        tenant_id: str = "default"
    ) -> List[str]:
        """List all keys in a namespace."""
        try:
            cursor = self.collection.find(
                {"namespace": list(namespace), "tenant_id": tenant_id},
                {"key": 1}
            )
            
            return [doc["key"] for doc in cursor]
            
        except Exception as e:
            logger.error(f"Failed to list namespace: {e}")
            return []
    
    def cleanup_old_data(self, days_old: int = 30) -> int:
        """Clean up old memory data."""
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days_old)
            result = self.collection.delete_many({
                "updated_at": {"$lt": cutoff_date}
            })

            logger.info(f"Cleaned up {result.deleted_count} old memory records")
            return result.deleted_count

        except Exception as e:
            logger.error(f"Failed to cleanup old data: {e}")
            return 0

    # Required abstract methods from BaseStore
    async def abatch(self, ops):
        """Async batch operations - not implemented for this simple store."""
        raise NotImplementedError("Async batch operations not supported")

    def batch(self, ops):
        """Batch operations - not implemented for this simple store."""
        raise NotImplementedError("Batch operations not supported")
