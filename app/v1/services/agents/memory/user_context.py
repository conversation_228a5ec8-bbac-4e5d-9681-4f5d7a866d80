# app/v1/services/agents/memory/user_context.py

from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from pymongo import MongoClient
from pymongo.collection import Collection

from app.shared.config.agent_config import agent_config
from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)

class UserContextManager:
    """
    Manages long-term user context and preferences.
    
    Handles:
    - User preferences and settings
    - Conversation summaries across sessions
    - Learning from user interactions
    - Personalization data
    """
    
    def __init__(self, mongo_uri: Optional[str] = None, db_name: Optional[str] = None):
        self.mongo_uri = mongo_uri or agent_config.memory_mongo_uri
        self.db_name = db_name or agent_config.AGENT_MEMORY_DB_NAME
        self.collection_name = "user_contexts"
        
        self.client = MongoClient(self.mongo_uri)
        self.db = self.client[self.db_name]
        self.collection: Collection = self.db[self.collection_name]
        
        # Create indexes
        self._create_indexes()
        
        logger.info(f"Initialized user context manager: {self.db_name}.{self.collection_name}")
    
    def _create_indexes(self):
        """Create necessary indexes for efficient querying."""
        try:
            # Unique index on user_id + tenant_id
            self.collection.create_index([("user_id", 1), ("tenant_id", 1)], unique=True)
            
            # Index on last_updated for cleanup operations
            self.collection.create_index("last_updated")
            
            # Index on tenant_id for multi-tenant queries
            self.collection.create_index("tenant_id")
            
            logger.info("Created MongoDB indexes for user context")
        except Exception as e:
            logger.warning(f"Failed to create user context indexes: {e}")
    
    def get_user_context(self, user_id: str, tenant_id: str) -> Dict[str, Any]:
        """
        Get user context data.
        
        Args:
            user_id: ID of the user
            tenant_id: ID of the tenant
            
        Returns:
            Dictionary containing user context data
        """
        try:
            document = self.collection.find_one({
                "user_id": user_id,
                "tenant_id": tenant_id
            })
            
            if document:
                return {
                    "user_id": document["user_id"],
                    "tenant_id": document["tenant_id"],
                    "preferences": document.get("preferences", {}),
                    "conversation_summaries": document.get("conversation_summaries", []),
                    "learned_topics": document.get("learned_topics", []),
                    "language_preference": document.get("language_preference", "nepali"),
                    "interaction_count": document.get("interaction_count", 0),
                    "last_updated": document.get("last_updated"),
                    "created_at": document.get("created_at")
                }
            else:
                # Return default context for new users
                return self._create_default_context(user_id, tenant_id)
                
        except Exception as e:
            logger.error(f"Failed to get user context for {user_id}: {e}")
            return self._create_default_context(user_id, tenant_id)
    
    def _create_default_context(self, user_id: str, tenant_id: str) -> Dict[str, Any]:
        """Create default context for new users."""
        return {
            "user_id": user_id,
            "tenant_id": tenant_id,
            "preferences": {
                "response_length": "medium",
                "include_sources": True,
                "max_results": 5
            },
            "conversation_summaries": [],
            "learned_topics": [],
            "language_preference": "nepali",
            "interaction_count": 0,
            "last_updated": None,
            "created_at": None
        }
    
    def update_user_context(
        self,
        user_id: str,
        tenant_id: str,
        preferences: Optional[Dict[str, Any]] = None,
        conversation_summary: Optional[str] = None,
        learned_topics: Optional[List[str]] = None,
        language_preference: Optional[str] = None
    ) -> bool:
        """
        Update user context data.
        
        Args:
            user_id: ID of the user
            tenant_id: ID of the tenant
            preferences: Updated preferences
            conversation_summary: New conversation summary to add
            learned_topics: New topics to add to learned topics
            language_preference: Updated language preference
            
        Returns:
            True if update successful, False otherwise
        """
        try:
            update_data = {
                "last_updated": datetime.utcnow(),
                "$inc": {"interaction_count": 1}
            }
            
            set_data = {}
            
            if preferences:
                set_data["preferences"] = preferences
            
            if language_preference:
                set_data["language_preference"] = language_preference
            
            if set_data:
                update_data["$set"] = set_data
            
            # Handle array operations
            if conversation_summary:
                # Add new summary and keep only last 10
                update_data["$push"] = {
                    "conversation_summaries": {
                        "$each": [{
                            "summary": conversation_summary,
                            "timestamp": datetime.utcnow().isoformat()
                        }],
                        "$slice": -10  # Keep only last 10 summaries
                    }
                }
            
            if learned_topics:
                # Add new topics (avoiding duplicates)
                update_data["$addToSet"] = {
                    "learned_topics": {"$each": learned_topics}
                }
            
            result = self.collection.update_one(
                {"user_id": user_id, "tenant_id": tenant_id},
                update_data,
                upsert=True
            )
            
            # Set created_at for new documents
            if result.upserted_id:
                self.collection.update_one(
                    {"_id": result.upserted_id},
                    {"$set": {"created_at": datetime.utcnow()}}
                )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to update user context for {user_id}: {e}")
            return False
    
    def get_user_preferences(self, user_id: str, tenant_id: str) -> Dict[str, Any]:
        """
        Get user preferences.
        
        Args:
            user_id: ID of the user
            tenant_id: ID of the tenant
            
        Returns:
            Dictionary containing user preferences
        """
        try:
            context = self.get_user_context(user_id, tenant_id)
            return context.get("preferences", {})
            
        except Exception as e:
            logger.error(f"Failed to get user preferences for {user_id}: {e}")
            return {}
    
    def get_conversation_summaries(
        self,
        user_id: str,
        tenant_id: str,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """
        Get recent conversation summaries for a user.
        
        Args:
            user_id: ID of the user
            tenant_id: ID of the tenant
            limit: Maximum number of summaries to return
            
        Returns:
            List of conversation summaries
        """
        try:
            document = self.collection.find_one(
                {"user_id": user_id, "tenant_id": tenant_id},
                {"conversation_summaries": {"$slice": -limit}}
            )
            
            if document and "conversation_summaries" in document:
                return document["conversation_summaries"]
            else:
                return []
                
        except Exception as e:
            logger.error(f"Failed to get conversation summaries for {user_id}: {e}")
            return []
    
    def get_learned_topics(self, user_id: str, tenant_id: str) -> List[str]:
        """
        Get topics the user has shown interest in.
        
        Args:
            user_id: ID of the user
            tenant_id: ID of the tenant
            
        Returns:
            List of learned topics
        """
        try:
            context = self.get_user_context(user_id, tenant_id)
            return context.get("learned_topics", [])
            
        except Exception as e:
            logger.error(f"Failed to get learned topics for {user_id}: {e}")
            return []
    
    def delete_user_context(self, user_id: str, tenant_id: str) -> bool:
        """
        Delete all context data for a user.
        
        Args:
            user_id: ID of the user
            tenant_id: ID of the tenant
            
        Returns:
            True if deletion successful, False otherwise
        """
        try:
            result = self.collection.delete_one({
                "user_id": user_id,
                "tenant_id": tenant_id
            })
            
            logger.info(f"Deleted user context for {user_id}")
            return result.deleted_count > 0
            
        except Exception as e:
            logger.error(f"Failed to delete user context for {user_id}: {e}")
            return False
    
    def get_tenant_stats(self, tenant_id: str) -> Dict[str, Any]:
        """
        Get statistics for a tenant.
        
        Args:
            tenant_id: ID of the tenant
            
        Returns:
            Dictionary containing tenant statistics
        """
        try:
            pipeline = [
                {"$match": {"tenant_id": tenant_id}},
                {
                    "$group": {
                        "_id": None,
                        "total_users": {"$sum": 1},
                        "total_interactions": {"$sum": "$interaction_count"},
                        "avg_interactions": {"$avg": "$interaction_count"},
                        "languages": {"$addToSet": "$language_preference"}
                    }
                },
                {
                    "$project": {
                        "_id": 0,
                        "total_users": 1,
                        "total_interactions": 1,
                        "avg_interactions": {"$round": ["$avg_interactions", 2]},
                        "languages": 1
                    }
                }
            ]
            
            result = list(self.collection.aggregate(pipeline))
            
            if result:
                return result[0]
            else:
                return {
                    "total_users": 0,
                    "total_interactions": 0,
                    "avg_interactions": 0,
                    "languages": []
                }
                
        except Exception as e:
            logger.error(f"Failed to get tenant stats for {tenant_id}: {e}")
            return {
                "total_users": 0,
                "total_interactions": 0,
                "avg_interactions": 0,
                "languages": [],
                "error": str(e)
            }
    
    def cleanup_old_contexts(self, days_old: int = 90) -> int:
        """
        Clean up old user contexts that haven't been updated recently.
        
        Args:
            days_old: Number of days after which to clean up contexts
            
        Returns:
            Number of contexts cleaned up
        """
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days_old)
            
            result = self.collection.delete_many({
                "last_updated": {"$lt": cutoff_date},
                "interaction_count": {"$lt": 5}  # Only clean up users with few interactions
            })
            
            logger.info(f"Cleaned up {result.deleted_count} old user contexts")
            return result.deleted_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup old contexts: {e}")
            return 0
