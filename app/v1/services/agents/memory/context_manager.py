# app/v1/services/agents/memory/context_manager.py

import time
import json
from typing import Dict, Any, List, Optional, Tu<PERSON>
from datetime import datetime, timedelta

from app.shared.utils.logger import setup_new_logging
from .mongo_memory import MongoMemoryStore

logger = setup_new_logging(__name__)

class ContextManager:
    """
    LSTM-like context memory manager for maintaining conversation state across interactions.
    
    Provides:
    - Conversation history management
    - User preference tracking
    - Context summarization
    - Memory consolidation
    - Cross-session context persistence
    """
    
    def __init__(self, memory_store: Optional[MongoMemoryStore] = None):
        self.memory_store = memory_store or MongoMemoryStore()
        self.max_conversation_length = 50  # Maximum messages to keep in active memory
        self.context_window_size = 10  # Number of recent messages for context
        logger.info("Initialized ContextManager")
    
    async def load_conversation_context(
        self,
        user_id: str,
        tenant_id: str,
        conversation_id: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Load conversation context and user preferences."""
        try:
            # Determine conversation key
            conv_key = conversation_id or session_id or f"session_{user_id}_{int(time.time())}"
            
            # Load conversation history
            conversation_data = self.memory_store.get(
                ("conversations", tenant_id),
                conv_key,
                tenant_id
            )
            
            # Load user preferences and profile
            user_profile = self.memory_store.get(
                ("user_profiles", tenant_id),
                user_id,
                tenant_id
            )
            
            # Load user interaction patterns
            user_patterns = self.memory_store.get(
                ("user_patterns", tenant_id),
                user_id,
                tenant_id
            )
            
            # Create context summary
            context_summary = await self._create_context_summary(
                conversation_data, user_profile, user_patterns
            )
            
            return {
                "conversation_id": conv_key,
                "history": conversation_data.get("messages", []) if conversation_data else [],
                "user_profile": user_profile or {},
                "user_patterns": user_patterns or {},
                "context_summary": context_summary,
                "total_interactions": len(conversation_data.get("messages", [])) if conversation_data else 0,
                "last_interaction": conversation_data.get("updated_at") if conversation_data else None
            }
            
        except Exception as e:
            logger.error(f"Error loading conversation context: {e}")
            return {
                "conversation_id": conv_key,
                "history": [],
                "user_profile": {},
                "user_patterns": {},
                "context_summary": None,
                "total_interactions": 0,
                "last_interaction": None
            }
    
    async def update_conversation_memory(
        self,
        user_id: str,
        tenant_id: str,
        conversation_id: str,
        user_query: str,
        agent_response: str,
        tool_results: List[Dict[str, Any]],
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Update conversation memory with new interaction."""
        try:
            # Create interaction entry
            interaction = {
                "timestamp": time.time(),
                "datetime": datetime.utcnow().isoformat(),
                "user_query": user_query,
                "agent_response": agent_response,
                "tools_used": [result.get("tool_name") for result in tool_results],
                "sources_count": sum(len(result.get("sources", [])) for result in tool_results),
                "metadata": metadata or {}
            }
            
            # Load existing conversation
            conversation_data = self.memory_store.get(
                ("conversations", tenant_id),
                conversation_id,
                tenant_id
            )
            
            if conversation_data:
                conversation_data["messages"].append(interaction)
                # Keep only recent messages to manage memory
                conversation_data["messages"] = conversation_data["messages"][-self.max_conversation_length:]
                conversation_data["updated_at"] = time.time()
                conversation_data["total_interactions"] = conversation_data.get("total_interactions", 0) + 1
            else:
                conversation_data = {
                    "user_id": user_id,
                    "tenant_id": tenant_id,
                    "created_at": time.time(),
                    "updated_at": time.time(),
                    "messages": [interaction],
                    "total_interactions": 1
                }
            
            # Save updated conversation
            self.memory_store.put(
                ("conversations", tenant_id),
                conversation_id,
                conversation_data,
                tenant_id
            )
            
            # Update user patterns
            await self._update_user_patterns(user_id, tenant_id, user_query, tool_results)
            
            # Consolidate memory if needed
            if len(conversation_data["messages"]) >= self.max_conversation_length:
                await self._consolidate_memory(user_id, tenant_id, conversation_id, conversation_data)
            
        except Exception as e:
            logger.error(f"Error updating conversation memory: {e}")
    
    async def update_user_preferences(
        self,
        user_id: str,
        tenant_id: str,
        preferences: Dict[str, Any]
    ) -> None:
        """Update user preferences and profile."""
        try:
            # Load existing profile
            existing_profile = self.memory_store.get(
                ("user_profiles", tenant_id),
                user_id,
                tenant_id
            ) or {}
            
            # Merge preferences
            existing_profile.update(preferences)
            existing_profile["updated_at"] = time.time()
            
            # Save updated profile
            self.memory_store.put(
                ("user_profiles", tenant_id),
                user_id,
                existing_profile,
                tenant_id
            )
            
        except Exception as e:
            logger.error(f"Error updating user preferences: {e}")
    
    async def get_relevant_context(
        self,
        user_id: str,
        tenant_id: str,
        conversation_id: str,
        current_query: str
    ) -> Dict[str, Any]:
        """Get relevant context for the current query."""
        try:
            # Load conversation context
            context = await self.load_conversation_context(user_id, tenant_id, conversation_id)
            
            # Get recent messages for immediate context
            recent_messages = context["history"][-self.context_window_size:]
            
            # Extract relevant topics and patterns
            relevant_topics = self._extract_relevant_topics(recent_messages, current_query)
            
            # Get user preferences relevant to the query
            relevant_preferences = self._get_relevant_preferences(
                context["user_profile"], current_query
            )
            
            return {
                "recent_messages": recent_messages,
                "relevant_topics": relevant_topics,
                "relevant_preferences": relevant_preferences,
                "context_summary": context["context_summary"],
                "total_interactions": context["total_interactions"]
            }
            
        except Exception as e:
            logger.error(f"Error getting relevant context: {e}")
            return {
                "recent_messages": [],
                "relevant_topics": [],
                "relevant_preferences": {},
                "context_summary": None,
                "total_interactions": 0
            }
    
    async def _create_context_summary(
        self,
        conversation_data: Optional[Dict[str, Any]],
        user_profile: Optional[Dict[str, Any]],
        user_patterns: Optional[Dict[str, Any]]
    ) -> Optional[str]:
        """Create a summary of the conversation context."""
        try:
            if not conversation_data or not conversation_data.get("messages"):
                return None
            
            messages = conversation_data["messages"]
            total_messages = len(messages)
            
            # Extract key topics from recent messages
            recent_topics = []
            for msg in messages[-5:]:  # Last 5 messages
                query = msg.get("user_query", "")
                if len(query) > 10:  # Only meaningful queries
                    recent_topics.append(query[:50] + "..." if len(query) > 50 else query)
            
            # Get user preferences summary
            preferences_summary = ""
            if user_profile:
                language = user_profile.get("preferred_language", "nepali")
                preferences_summary = f" User prefers {language} language."
            
            # Get interaction patterns
            patterns_summary = ""
            if user_patterns:
                common_tools = user_patterns.get("most_used_tools", [])
                if common_tools:
                    patterns_summary = f" Commonly uses: {', '.join(common_tools[:3])}."
            
            summary = f"Conversation with {total_messages} exchanges.{preferences_summary}{patterns_summary}"
            if recent_topics:
                summary += f" Recent topics: {', '.join(recent_topics)}"
            
            return summary
            
        except Exception as e:
            logger.warning(f"Error creating context summary: {e}")
            return None
    
    async def _update_user_patterns(
        self,
        user_id: str,
        tenant_id: str,
        user_query: str,
        tool_results: List[Dict[str, Any]]
    ) -> None:
        """Update user interaction patterns."""
        try:
            # Load existing patterns
            patterns = self.memory_store.get(
                ("user_patterns", tenant_id),
                user_id,
                tenant_id
            ) or {
                "query_types": {},
                "tool_usage": {},
                "interaction_times": [],
                "total_queries": 0
            }
            
            # Update query type patterns
            query_length = len(user_query.split())
            query_type = "short" if query_length < 5 else "medium" if query_length < 15 else "long"
            patterns["query_types"][query_type] = patterns["query_types"].get(query_type, 0) + 1
            
            # Update tool usage patterns
            for result in tool_results:
                tool_name = result.get("tool_name")
                if tool_name:
                    patterns["tool_usage"][tool_name] = patterns["tool_usage"].get(tool_name, 0) + 1
            
            # Update interaction times
            current_hour = datetime.now().hour
            patterns["interaction_times"].append(current_hour)
            # Keep only last 100 interaction times
            patterns["interaction_times"] = patterns["interaction_times"][-100:]
            
            patterns["total_queries"] += 1
            patterns["updated_at"] = time.time()
            
            # Calculate most used tools
            if patterns["tool_usage"]:
                sorted_tools = sorted(patterns["tool_usage"].items(), key=lambda x: x[1], reverse=True)
                patterns["most_used_tools"] = [tool for tool, count in sorted_tools[:5]]
            
            # Save updated patterns
            self.memory_store.put(
                ("user_patterns", tenant_id),
                user_id,
                patterns,
                tenant_id
            )
            
        except Exception as e:
            logger.warning(f"Error updating user patterns: {e}")
    
    async def _consolidate_memory(
        self,
        user_id: str,
        tenant_id: str,
        conversation_id: str,
        conversation_data: Dict[str, Any]
    ) -> None:
        """Consolidate older conversation memory to save space."""
        try:
            messages = conversation_data["messages"]
            
            # Keep recent messages and create summary of older ones
            recent_messages = messages[-20:]  # Keep last 20 messages
            older_messages = messages[:-20]
            
            if older_messages:
                # Create summary of older messages
                summary_data = {
                    "period_start": older_messages[0]["timestamp"],
                    "period_end": older_messages[-1]["timestamp"],
                    "message_count": len(older_messages),
                    "topics": [msg["user_query"][:50] for msg in older_messages[-5:]],
                    "tools_used": list(set(tool for msg in older_messages for tool in msg.get("tools_used", []))),
                    "created_at": time.time()
                }
                
                # Save summary
                summary_key = f"{conversation_id}_summary_{int(time.time())}"
                self.memory_store.put(
                    ("conversation_summaries", tenant_id),
                    summary_key,
                    summary_data,
                    tenant_id
                )
                
                # Update conversation with only recent messages
                conversation_data["messages"] = recent_messages
                conversation_data["has_summaries"] = True
                
                self.memory_store.put(
                    ("conversations", tenant_id),
                    conversation_id,
                    conversation_data,
                    tenant_id
                )
                
                logger.info(f"Consolidated {len(older_messages)} older messages for conversation {conversation_id}")
            
        except Exception as e:
            logger.warning(f"Error consolidating memory: {e}")
    
    def _extract_relevant_topics(self, messages: List[Dict[str, Any]], current_query: str) -> List[str]:
        """Extract topics relevant to the current query from recent messages."""
        relevant_topics = []
        current_query_lower = current_query.lower()
        
        for msg in messages:
            query = msg.get("user_query", "").lower()
            # Simple relevance check - can be enhanced with NLP
            if any(word in query for word in current_query_lower.split() if len(word) > 3):
                relevant_topics.append(msg.get("user_query", "")[:100])
        
        return relevant_topics[-3:]  # Return last 3 relevant topics
    
    def _get_relevant_preferences(self, user_profile: Dict[str, Any], current_query: str) -> Dict[str, Any]:
        """Get user preferences relevant to the current query."""
        relevant_prefs = {}
        
        # Always include language preference
        if "preferred_language" in user_profile:
            relevant_prefs["language"] = user_profile["preferred_language"]
        
        # Include other relevant preferences based on query content
        # This can be enhanced with more sophisticated matching
        if "legal" in current_query.lower():
            if "legal_specialization" in user_profile:
                relevant_prefs["specialization"] = user_profile["legal_specialization"]
        
        return relevant_prefs
