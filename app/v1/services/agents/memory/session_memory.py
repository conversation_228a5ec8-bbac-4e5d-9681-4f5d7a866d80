# app/v1/services/agents/memory/session_memory.py

import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from pymongo import MongoClient
from pymongo.collection import Collection

from app.shared.config.agent_config import agent_config
from app.shared.utils.logger import setup_new_logging
from ..types import AgentMemory

logger = setup_new_logging(__name__)

class SessionMemoryManager:
    """
    Manages session-based memory for conversations.
    
    Handles:
    - Session creation and management
    - Conversation history storage
    - Session cleanup and expiration
    - Cross-session context retrieval
    """
    
    def __init__(self, mongo_uri: Optional[str] = None, db_name: Optional[str] = None):
        self.mongo_uri = mongo_uri or agent_config.memory_mongo_uri
        self.db_name = db_name or agent_config.AGENT_MEMORY_DB_NAME
        self.collection_name = agent_config.AGENT_SESSION_COLLECTION
        
        self.client = MongoClient(self.mongo_uri)
        self.db = self.client[self.db_name]
        self.collection: Collection = self.db[self.collection_name]
        
        # Create indexes
        self._create_indexes()
        
        logger.info(f"Initialized session memory manager: {self.db_name}.{self.collection_name}")
    
    def _create_indexes(self):
        """Create necessary indexes for efficient querying."""
        try:
            # Index on session_id for fast session retrieval
            self.collection.create_index("session_id", unique=True)
            
            # Index on user_id for user-based queries
            self.collection.create_index("user_id")
            
            # Index on tenant_id for multi-tenant isolation
            self.collection.create_index("tenant_id")
            
            # Index on last_updated for cleanup operations
            self.collection.create_index("last_updated")
            
            # Compound index for user sessions
            self.collection.create_index([("user_id", 1), ("tenant_id", 1), ("last_updated", -1)])
            
            logger.info("Created MongoDB indexes for session memory")
        except Exception as e:
            logger.warning(f"Failed to create session memory indexes: {e}")
    
    def create_session(
        self,
        user_id: str,
        tenant_id: str,
        conversation_id: Optional[str] = None
    ) -> str:
        """
        Create a new session.
        
        Args:
            user_id: ID of the user
            tenant_id: ID of the tenant
            conversation_id: Optional conversation ID to link
            
        Returns:
            Session ID
        """
        try:
            session_id = str(uuid.uuid4())
            
            session_data = {
                "session_id": session_id,
                "user_id": user_id,
                "tenant_id": tenant_id,
                "conversation_id": conversation_id,
                "conversation_history": [],
                "user_preferences": {},
                "context_summary": None,
                "created_at": datetime.utcnow(),
                "last_updated": datetime.utcnow(),
                "active": True
            }
            
            self.collection.insert_one(session_data)
            
            logger.info(f"Created session {session_id} for user {user_id}")
            return session_id
            
        except Exception as e:
            logger.error(f"Failed to create session: {e}")
            raise
    
    def get_session(self, session_id: str) -> Optional[AgentMemory]:
        """
        Retrieve session data.
        
        Args:
            session_id: ID of the session
            
        Returns:
            AgentMemory object or None if not found
        """
        try:
            document = self.collection.find_one({"session_id": session_id})
            
            if not document:
                return None
            
            return AgentMemory(
                session_id=document["session_id"],
                user_id=document["user_id"],
                conversation_history=document.get("conversation_history", []),
                user_preferences=document.get("user_preferences", {}),
                context_summary=document.get("context_summary"),
                last_updated=document.get("last_updated", datetime.utcnow())
            )
            
        except Exception as e:
            logger.error(f"Failed to retrieve session {session_id}: {e}")
            return None
    
    def update_session(
        self,
        session_id: str,
        conversation_history: Optional[List[Dict[str, Any]]] = None,
        user_preferences: Optional[Dict[str, Any]] = None,
        context_summary: Optional[str] = None
    ) -> bool:
        """
        Update session data.
        
        Args:
            session_id: ID of the session
            conversation_history: Updated conversation history
            user_preferences: Updated user preferences
            context_summary: Updated context summary
            
        Returns:
            True if update successful, False otherwise
        """
        try:
            update_data = {"last_updated": datetime.utcnow()}
            
            if conversation_history is not None:
                update_data["conversation_history"] = conversation_history
            
            if user_preferences is not None:
                update_data["user_preferences"] = user_preferences
            
            if context_summary is not None:
                update_data["context_summary"] = context_summary
            
            result = self.collection.update_one(
                {"session_id": session_id},
                {"$set": update_data}
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Failed to update session {session_id}: {e}")
            return False
    
    def add_to_conversation_history(
        self,
        session_id: str,
        message: Dict[str, Any]
    ) -> bool:
        """
        Add a message to the conversation history.
        
        Args:
            session_id: ID of the session
            message: Message to add
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Add timestamp if not present
            if "timestamp" not in message:
                message["timestamp"] = datetime.utcnow().isoformat()
            
            result = self.collection.update_one(
                {"session_id": session_id},
                {
                    "$push": {"conversation_history": message},
                    "$set": {"last_updated": datetime.utcnow()}
                }
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Failed to add message to session {session_id}: {e}")
            return False
    
    def get_user_sessions(
        self,
        user_id: str,
        tenant_id: str,
        limit: int = 10,
        active_only: bool = True
    ) -> List[Dict[str, Any]]:
        """
        Get sessions for a user.
        
        Args:
            user_id: ID of the user
            tenant_id: ID of the tenant
            limit: Maximum number of sessions to return
            active_only: Whether to return only active sessions
            
        Returns:
            List of session data
        """
        try:
            query = {"user_id": user_id, "tenant_id": tenant_id}
            if active_only:
                query["active"] = True
            
            cursor = self.collection.find(
                query,
                {"conversation_history": 0}  # Exclude large conversation history
            ).sort("last_updated", -1).limit(limit)
            
            return list(cursor)
            
        except Exception as e:
            logger.error(f"Failed to get user sessions: {e}")
            return []
    
    def deactivate_session(self, session_id: str) -> bool:
        """
        Deactivate a session.
        
        Args:
            session_id: ID of the session
            
        Returns:
            True if successful, False otherwise
        """
        try:
            result = self.collection.update_one(
                {"session_id": session_id},
                {
                    "$set": {
                        "active": False,
                        "last_updated": datetime.utcnow()
                    }
                }
            )
            
            return result.modified_count > 0
            
        except Exception as e:
            logger.error(f"Failed to deactivate session {session_id}: {e}")
            return False
    
    def cleanup_old_sessions(self, days_old: int = 7) -> int:
        """
        Clean up old inactive sessions.
        
        Args:
            days_old: Number of days after which to clean up sessions
            
        Returns:
            Number of sessions cleaned up
        """
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days_old)
            
            result = self.collection.delete_many({
                "active": False,
                "last_updated": {"$lt": cutoff_date}
            })
            
            logger.info(f"Cleaned up {result.deleted_count} old sessions")
            return result.deleted_count
            
        except Exception as e:
            logger.error(f"Failed to cleanup old sessions: {e}")
            return 0
    
    def get_session_stats(self, tenant_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get statistics about sessions.
        
        Args:
            tenant_id: Optional tenant ID to filter by
            
        Returns:
            Dictionary containing session statistics
        """
        try:
            pipeline = []
            
            # Match stage
            match_stage = {}
            if tenant_id:
                match_stage["tenant_id"] = tenant_id
            
            if match_stage:
                pipeline.append({"$match": match_stage})
            
            # Group stage
            pipeline.append({
                "$group": {
                    "_id": None,
                    "total_sessions": {"$sum": 1},
                    "active_sessions": {
                        "$sum": {"$cond": [{"$eq": ["$active", True]}, 1, 0]}
                    },
                    "unique_users": {"$addToSet": "$user_id"}
                }
            })
            
            # Project stage
            pipeline.append({
                "$project": {
                    "_id": 0,
                    "total_sessions": 1,
                    "active_sessions": 1,
                    "unique_users": {"$size": "$unique_users"}
                }
            })
            
            result = list(self.collection.aggregate(pipeline))
            
            if result:
                return result[0]
            else:
                return {
                    "total_sessions": 0,
                    "active_sessions": 0,
                    "unique_users": 0
                }
                
        except Exception as e:
            logger.error(f"Failed to get session stats: {e}")
            return {
                "total_sessions": 0,
                "active_sessions": 0,
                "unique_users": 0,
                "error": str(e)
            }
