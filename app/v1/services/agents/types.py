# app/v1/services/agents/types.py

from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum

class AgentType(str, Enum):
    """Types of agents in the system."""
    ORCHESTRATOR = "orchestrator"  # Legacy orchestrator (deprecated)
    MAIN_AGENT = "main_agent"  # New LLM-powered main agent
    DOCUMENT_SEARCH = "document_search"
    GENERAL_CHAT = "general_chat"
    ANALYSIS = "analysis"
    DATA_RETRIEVAL = "data_retrieval"

class AgentStatus(str, Enum):
    """Status of agent execution."""
    SUCCESS = "success"
    ERROR = "error"
    TIMEOUT = "timeout"
    PARTIAL = "partial"

# Import uniform response models from chat models
from app.v1.services.chat.models import SentenceData, SourceGroup, UniformResponse

class SourceReference(BaseModel):
    """Reference to a source document or chunk (legacy)."""
    document_id: str
    filename: str
    page_number: Optional[int] = None
    chunk_id: Optional[str] = None
    text_snippet: str
    confidence_score: Optional[float] = None
    minio_path: Optional[str] = None
    presigned_url: Optional[str] = None
    coordinates: Optional[Dict] = None

class AgentResponse(BaseModel):
    """Standardized response from any agent (updated for uniform format)."""
    agent_type: AgentType
    agent_name: str
    status: AgentStatus
    content: str
    uniform_sources: UniformResponse = Field(default_factory=lambda: UniformResponse(sources=[], total_sources_found=0))  # New uniform format
    sources: List[SourceReference] = Field(default_factory=list)  # Legacy format for backward compatibility
    metadata: Dict[str, Any] = Field(default_factory=dict)
    processing_time_ms: Optional[float] = None
    timestamp: datetime = Field(default_factory=datetime.now)
    error_message: Optional[str] = None

    class Config:
        use_enum_values = True

class AgentContext(BaseModel):
    """Context passed to agents for execution."""
    user_id: str
    tenant_id: str
    conversation_id: Optional[str] = None
    session_id: Optional[str] = None
    user_query: str
    language: str = "nepali"
    max_results: int = 5
    include_sources: bool = True
    additional_context: Dict[str, Any] = Field(default_factory=dict)

class AgentMemory(BaseModel):
    """Memory structure for agents."""
    session_id: str
    user_id: str
    conversation_history: List[Dict[str, Any]] = Field(default_factory=list)
    user_preferences: Dict[str, Any] = Field(default_factory=dict)
    context_summary: Optional[str] = None
    last_updated: datetime = Field(default_factory=datetime.now)

class AgentConfig(BaseModel):
    """Configuration for individual agents."""
    name: str
    agent_type: AgentType
    enabled: bool = True
    timeout_seconds: int = 30
    max_retries: int = 3
    config: Dict[str, Any] = Field(default_factory=dict)
