# app/v1/services/agents/tool_selection.py

import json
from typing import Dict, Any, List, Optional, Tu<PERSON>
from langchain_openai import ChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage
from langchain_core.tools import tool
from pydantic import BaseModel, Field

from app.shared.config.agent_config import agent_config
from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)

class ToolSelectionDecision(BaseModel):
    """Structured output for tool selection decisions."""
    selected_tools: List[str] = Field(description="List of tool names to execute")
    reasoning: str = Field(description="Explanation for tool selection")
    priority_order: List[str] = Field(description="Order in which tools should be executed")
    confidence: float = Field(description="Confidence score (0.0 to 1.0) for the decision")
    requires_context: bool = Field(description="Whether the query requires conversation context")

class ToolSelector:
    """
    LLM-based tool selection mechanism that analyzes user queries and dynamically
    determines which sub-agents or tools to invoke.
    
    Completely removes hardcoded routing patterns and uses intelligent LLM reasoning.
    """
    
    def __init__(self):
        self.llm = ChatOpenAI(
            model=agent_config.AGENT_LLM_MODEL,
            temperature=0.1,  # Lower temperature for more consistent tool selection
            max_tokens=500  # Shorter responses for tool selection
        )
        
        # Available tools and their descriptions
        self.available_tools = {
            "search_documents": {
                "description": "Search legal documents and retrieve relevant information",
                "use_cases": [
                    "Finding specific legal provisions",
                    "Searching for case law or precedents",
                    "Looking up statutory requirements",
                    "Retrieving document content",
                    "Finding relevant legal texts"
                ],
                "keywords": ["search", "find", "look up", "document", "law", "provision", "case", "statute"]
            },
            "general_chat": {
                "description": "Handle general legal questions and conversations",
                "use_cases": [
                    "General legal advice",
                    "Explaining legal concepts",
                    "Conversational queries",
                    "Legal education",
                    "Simple Q&A"
                ],
                "keywords": ["explain", "what is", "how to", "advice", "help", "understand"]
            },
            "analyze_legal_content": {
                "description": "Perform detailed legal analysis on provided content",
                "use_cases": [
                    "Contract analysis",
                    "Legal document review",
                    "Compliance checking",
                    "Risk assessment",
                    "Legal interpretation"
                ],
                "keywords": ["analyze", "review", "check", "interpret", "assess", "examine"]
            }
        }
        
        logger.info("Initialized ToolSelector with LLM-based routing")
    
    async def select_tools(
        self,
        user_query: str,
        conversation_context: Optional[Dict[str, Any]] = None,
        user_preferences: Optional[Dict[str, Any]] = None
    ) -> ToolSelectionDecision:
        """
        Select appropriate tools for the given user query using LLM reasoning.
        
        Args:
            user_query: The user's query
            conversation_context: Optional conversation context
            user_preferences: Optional user preferences
            
        Returns:
            ToolSelectionDecision with selected tools and reasoning
        """
        try:
            # Create tool selection prompt
            selection_prompt = self._create_tool_selection_prompt(
                user_query, conversation_context, user_preferences
            )
            
            # Get LLM decision with structured output
            llm_with_structure = self.llm.with_structured_output(ToolSelectionDecision)
            decision = await llm_with_structure.ainvoke([
                SystemMessage(content=self._get_tool_selection_system_prompt()),
                HumanMessage(content=selection_prompt)
            ])
            
            # Validate and adjust decision
            validated_decision = self._validate_tool_selection(decision, user_query)
            
            logger.info(f"Selected tools for query '{user_query[:50]}...': {validated_decision.selected_tools}")
            return validated_decision
            
        except Exception as e:
            logger.error(f"Error in tool selection: {e}")
            return self._get_fallback_tool_selection(user_query)
    
    def _get_tool_selection_system_prompt(self) -> str:
        """Get the system prompt for tool selection."""
        tools_description = "\n".join([
            f"- {name}: {info['description']}"
            for name, info in self.available_tools.items()
        ])
        
        return f"""You are an intelligent tool selector for a legal assistant system. Your job is to analyze user queries and select the most appropriate tools to handle them.

Available Tools:
{tools_description}

Your responsibilities:
1. Analyze the user query to understand intent and requirements
2. Select the most appropriate tool(s) based on the query type
3. Provide clear reasoning for your selection
4. Determine the optimal execution order if multiple tools are needed
5. Assess your confidence in the decision

Guidelines:
- Use search_documents for queries requiring specific legal information from documents
- Use general_chat for conversational queries or general legal advice
- Use analyze_legal_content when detailed analysis of provided content is needed
- You can select multiple tools if the query requires it
- Consider conversation context when making decisions
- Prioritize accuracy and relevance over speed
- Be conservative - if unsure, prefer search_documents for legal queries

Output your decision as a structured response with:
- selected_tools: List of tool names to use
- reasoning: Clear explanation of why these tools were chosen
- priority_order: Order of execution (same as selected_tools if sequential)
- confidence: Your confidence level (0.0 to 1.0)
- requires_context: Whether conversation context is important for this query
"""
    
    def _create_tool_selection_prompt(
        self,
        user_query: str,
        conversation_context: Optional[Dict[str, Any]] = None,
        user_preferences: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create the prompt for tool selection."""
        prompt_parts = [
            f"User Query: {user_query}",
            "",
            "Query Analysis Required:",
            "1. What is the user asking for?",
            "2. What type of information or action is needed?",
            "3. Which tool(s) would best serve this request?",
            "4. Is this a search query, conversational query, or analysis request?",
            ""
        ]
        
        # Add conversation context if available
        if conversation_context and conversation_context.get("history"):
            recent_messages = conversation_context["history"][-3:]  # Last 3 messages
            prompt_parts.extend([
                "Recent Conversation Context:",
                *[f"- User: {msg.get('user_query', '')[:100]}" for msg in recent_messages],
                ""
            ])
        
        # Add user preferences if available
        if user_preferences:
            prompt_parts.extend([
                "User Preferences:",
                f"- Language: {user_preferences.get('preferred_language', 'nepali')}",
                ""
            ])
        
        # Add tool selection criteria
        prompt_parts.extend([
            "Tool Selection Criteria:",
            "- search_documents: Use when user needs specific legal information, documents, or provisions",
            "- general_chat: Use for explanations, general advice, or conversational queries",
            "- analyze_legal_content: Use when user provides content that needs analysis",
            "",
            "Consider:",
            "- Query intent and complexity",
            "- Information requirements",
            "- User's apparent expertise level",
            "- Conversation context",
            "",
            "Select the most appropriate tool(s) and provide your reasoning."
        ])
        
        return "\n".join(prompt_parts)
    
    def _validate_tool_selection(self, decision: ToolSelectionDecision, user_query: str) -> ToolSelectionDecision:
        """Validate and adjust the tool selection decision."""
        # Ensure selected tools are valid
        valid_tools = [tool for tool in decision.selected_tools if tool in self.available_tools]
        
        if not valid_tools:
            # Fallback to search_documents for any legal query
            logger.warning(f"No valid tools selected, falling back to search_documents")
            valid_tools = ["search_documents"]
            decision.reasoning += " (Fallback: defaulted to document search)"
            decision.confidence = min(decision.confidence, 0.5)
        
        # Ensure priority order matches selected tools
        if len(decision.priority_order) != len(valid_tools):
            decision.priority_order = valid_tools
        
        # Ensure confidence is within bounds
        decision.confidence = max(0.0, min(1.0, decision.confidence))
        
        # Update selected tools
        decision.selected_tools = valid_tools
        
        return decision
    
    def _get_fallback_tool_selection(self, user_query: str) -> ToolSelectionDecision:
        """Get fallback tool selection when LLM selection fails."""
        # Simple heuristic-based fallback
        query_lower = user_query.lower()
        
        if any(keyword in query_lower for keyword in ["search", "find", "look", "document", "law", "provision"]):
            selected_tool = "search_documents"
            reasoning = "Fallback: Query appears to be a search request"
        elif any(keyword in query_lower for keyword in ["analyze", "review", "check", "examine"]):
            selected_tool = "analyze_legal_content"
            reasoning = "Fallback: Query appears to request analysis"
        else:
            selected_tool = "search_documents"  # Default fallback
            reasoning = "Fallback: Default to document search for legal queries"
        
        return ToolSelectionDecision(
            selected_tools=[selected_tool],
            reasoning=reasoning,
            priority_order=[selected_tool],
            confidence=0.3,  # Low confidence for fallback
            requires_context=False
        )
    
    def get_tool_capabilities(self) -> Dict[str, Any]:
        """Get information about available tools and their capabilities."""
        return {
            "available_tools": list(self.available_tools.keys()),
            "tool_descriptions": {
                name: info["description"] 
                for name, info in self.available_tools.items()
            },
            "selection_method": "llm_powered",
            "supports_multi_tool": True,
            "supports_context": True
        }
    
    async def explain_tool_selection(
        self,
        user_query: str,
        decision: ToolSelectionDecision
    ) -> str:
        """Provide a detailed explanation of why specific tools were selected."""
        try:
            explanation_prompt = f"""
User Query: {user_query}

Selected Tools: {', '.join(decision.selected_tools)}
Reasoning: {decision.reasoning}
Confidence: {decision.confidence:.2f}

Please provide a detailed, user-friendly explanation in Nepali of why these specific tools were chosen for this query. Explain how each tool will help address the user's needs.
"""
            
            response = await self.llm.ainvoke([
                SystemMessage(content="You are a helpful assistant that explains tool selection decisions to users in a clear and friendly manner. Always respond in Nepali."),
                HumanMessage(content=explanation_prompt)
            ])
            
            return response.content
            
        except Exception as e:
            logger.error(f"Error explaining tool selection: {e}")
            return f"तपाईंको प्रश्न \"{user_query}\" को लागि {', '.join(decision.selected_tools)} उपकरण चयन गरिएको छ।"
