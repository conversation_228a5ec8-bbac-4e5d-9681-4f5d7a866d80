# app/v1/services/agents/orchestrator.py

import time
from typing import Optional, Dict, Any

from app.shared.database.models import UserTenantDB
from app.shared.config.agent_config import agent_config
from .base_agent import BaseAgent
from .types import Agent<PERSON><PERSON>ponse, Agent<PERSON>ontext, AgentType, AgentStatus
from .registry import agent_registry

class OrchestratorAgent(BaseAgent):
    """
    Main orchestrator agent that routes user queries to appropriate sub-agents.
    
    The orchestrator:
    1. Analyzes incoming user queries
    2. Determines the best agent to handle the query
    3. Routes the query to the selected agent
    4. Handles fallback responses when no specific agent is suitable
    5. Manages conversation flow and context
    
    Routing Logic:
    - Document search queries → DocumentSearchAgent
    - General legal questions → GeneralChatAgent (if available)
    - Fallback → Direct response from orchestrator
    """
    
    def __init__(self):
        super().__init__(
            name="orchestrator",
            agent_type=AgentType.ORCHESTRATOR,
            timeout_seconds=agent_config.AGENT_DEFAULT_TIMEOUT + 10,  # Extra time for routing
            max_retries=agent_config.AGENT_MAX_RETRIES
        )
        

        

    
    async def _process_query(self, context: AgentContext, current_user: UserTenantDB) -> AgentResponse:
        """
        Process a query by routing it to the appropriate agent.
        
        Args:
            context: The context containing user query and metadata
            
        Returns:
            AgentResponse: Response from the selected agent or fallback response
        """
        start_time = time.time()
        
        try:
            # Determine the best agent for this query
            selected_agent_name = await self._route_query(context)
            
            if selected_agent_name:
                # Route to selected agent
                selected_agent = agent_registry.get_agent(selected_agent_name)
                if selected_agent:
                    self.logger.info(f"Routing query to agent: {selected_agent_name}")
                    
                    # Pass the actual current_user instead of creating mock user
                    
                    # Execute the selected agent
                    response = await selected_agent.run(context, current_user)
                    
                    # Add orchestrator metadata
                    response.metadata.update({
                        "routed_to": selected_agent_name,
                        "orchestrator_processing_time_ms": (time.time() - start_time) * 1000
                    })
                    
                    return response
                else:
                    self.logger.warning(f"Selected agent {selected_agent_name} not found in registry")
            
            # Fallback: Handle query directly
            return await self._handle_fallback(context, start_time)
            
        except Exception as e:
            self.logger.error(f"Error in orchestrator: {e}")
            
            processing_time = (time.time() - start_time) * 1000
            return AgentResponse(
                agent_type=self.agent_type,
                agent_name=self.name,
                status=AgentStatus.ERROR,
                content="माफ गर्नुहोस्, केही समस्या भयो। कृपया फेरि प्रयास गर्नुहोस्।",
                processing_time_ms=processing_time,
                error_message=str(e)
            )
    
    async def _route_query(self, context: AgentContext) -> Optional[str]:
        """
        Simple dynamic routing - just route to document search agent for all queries.
        """
        try:
            # Get available document search agents
            doc_agents = agent_registry.get_agents_by_type(AgentType.DOCUMENT_SEARCH)
            if doc_agents:
                self.logger.info(f"Routing to document search agent: {doc_agents[0].name}")
                return doc_agents[0].name

            self.logger.warning("No document search agents available")
            return None

        except Exception as e:
            self.logger.error(f"Error in query routing: {e}")
            return None
    
    async def _handle_fallback(self, context: AgentContext, start_time: float) -> AgentResponse:
        """
        Simple fallback - just return a basic response.
        """
        processing_time = (time.time() - start_time) * 1000

        return AgentResponse(
            agent_type=self.agent_type,
            agent_name=self.name,
            status=AgentStatus.SUCCESS,
            content="माफ गर्नुहोस्, म तपाईंको प्रश्नको जवाफ दिन सक्दिन। कृपया अझ स्पष्ट प्रश्न सोध्नुहोस्।",
            metadata={
                "fallback_response": True,
                "available_agents": agent_registry.list_agents()
            },
            processing_time_ms=processing_time
        )
    
    def get_routing_info(self) -> Dict[str, Any]:
        """Get information about routing capabilities."""
        return {
            "available_agents": agent_registry.list_agents(),
            "routing_keywords": {
                "document_search": self.document_search_keywords[:5],  # Show first 5
                "question_patterns": self.question_patterns[:3]  # Show first 3
            },
            "fallback_enabled": True,
            "llm_classification": True
        }
