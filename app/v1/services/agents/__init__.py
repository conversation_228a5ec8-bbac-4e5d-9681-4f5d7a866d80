# app/v1/services/agents/__init__.py

"""
Multi-agent system for legal assistant application.

This package provides a LangGraph-based multi-agent system with:
- Base agent classes and interfaces
- Orchestrator for routing queries to appropriate agents
- Memory management for conversation and user context
- Extensible agent registry for adding new agents
"""

from .base_agent import BaseAgent, AgentResponse, AgentError
from .orchestrator import OrchestratorAgent
from .registry import AgentRegistry

__all__ = [
    "BaseAgent",
    "AgentResponse", 
    "AgentError",
    "OrchestratorAgent",
    "AgentRegistry"
]
