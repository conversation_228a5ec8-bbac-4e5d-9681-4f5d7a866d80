# app/v1/services/agents/base_agent.py

import asyncio
import time
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from langgraph.graph import StateGraph, MessagesState, START, END
from langgraph.checkpoint.memory import InMemorySaver

from app.shared.utils.logger import setup_new_logging
from app.shared.database.models import UserTenantDB
from .types import AgentResponse, AgentContext, AgentType, AgentStatus

logger = setup_new_logging(__name__)

class AgentError(Exception):
    """Custom exception for agent errors."""
    def __init__(self, message: str, agent_name: str, error_type: str = "general"):
        self.message = message
        self.agent_name = agent_name
        self.error_type = error_type
        super().__init__(f"Agent {agent_name} error ({error_type}): {message}")

class BaseAgent(ABC):
    """
    Base class for all agents in the system.
    
    Provides common functionality for:
    - LangGraph integration
    - Error handling
    - Timeout management
    - Response formatting
    - Memory management
    """
    
    def __init__(
        self,
        name: str,
        agent_type: AgentType,
        timeout_seconds: int = 30,
        max_retries: int = 3
    ):
        self.name = name
        self.agent_type = agent_type
        self.timeout_seconds = timeout_seconds
        self.max_retries = max_retries
        self.logger = setup_new_logging(f"{__name__}.{name}")
        
        # Initialize LangGraph components
        self.checkpointer = InMemorySaver()
        self.graph = None
        self._initialize_graph()
    
    def _initialize_graph(self):
        """Initialize the LangGraph state graph for this agent."""
        try:
            builder = StateGraph(MessagesState)
            builder.add_node("process", self._process_node)
            builder.add_edge(START, "process")
            builder.add_edge("process", END)
            
            self.graph = builder.compile(checkpointer=self.checkpointer)
            self.logger.info(f"Initialized LangGraph for agent {self.name}")
        except Exception as e:
            self.logger.error(f"Failed to initialize LangGraph for agent {self.name}: {e}")
            raise AgentError(f"Graph initialization failed: {e}", self.name, "initialization")
    
    async def _process_node(self, state: MessagesState) -> Dict[str, Any]:
        """
        LangGraph node that processes the user message.
        This calls the agent's specific processing logic.
        """
        try:
            # Extract the latest user message
            messages = state.get("messages", [])
            if not messages:
                raise AgentError("No messages in state", self.name, "input")
            
            latest_message = messages[-1]
            if not isinstance(latest_message, HumanMessage):
                raise AgentError("Latest message is not from human", self.name, "input")
            
            # Create context from the message
            context = AgentContext(
                user_id="system",  # Will be overridden by actual implementation
                tenant_id="system",
                user_query=latest_message.content,
                conversation_id=state.get("conversation_id"),
                session_id=state.get("session_id")
            )
            
            # Process the query using agent-specific logic
            response = await self._process_query(context)
            
            # Create AI message with the response
            ai_message = AIMessage(
                content=response.content,
                additional_kwargs={
                    "agent_type": response.agent_type,
                    "agent_name": response.agent_name,
                    "status": response.status,
                    "sources": [source.dict() for source in response.sources],
                    "metadata": response.metadata,
                    "processing_time_ms": response.processing_time_ms
                }
            )
            
            return {"messages": [ai_message]}
            
        except Exception as e:
            self.logger.error(f"Error in process node for agent {self.name}: {e}")

            # Provide more specific error messages based on error type
            if "timeout" in str(e).lower():
                error_content = "माफ गर्नुहोस्, प्रतिक्रिया दिन धेरै समय लाग्यो। कृपया फेरि प्रयास गर्नुहोस्।"
            elif "connection" in str(e).lower() or "network" in str(e).lower():
                error_content = "माफ गर्नुहोस्, नेटवर्क समस्या भयो। कृपया फेरि प्रयास गर्नुहोस्।"
            elif "permission" in str(e).lower() or "unauthorized" in str(e).lower():
                error_content = "माफ गर्नुहोस्, तपाईंलाई यो कार्य गर्ने अनुमति छैन।"
            else:
                error_content = "माफ गर्नुहोस्, केही समस्या भयो। कृपया फेरि प्रयास गर्नुहोस्।"

            error_message = AIMessage(
                content=error_content,
                additional_kwargs={
                    "agent_type": self.agent_type,
                    "agent_name": self.name,
                    "status": AgentStatus.ERROR,
                    "error_message": str(e),
                    "error_type": type(e).__name__
                }
            )
            return {"messages": [error_message]}
    
    @abstractmethod
    async def _process_query(self, context: AgentContext) -> AgentResponse:
        """
        Process a user query and return a response.
        This method must be implemented by each specific agent.
        
        Args:
            context: The context containing user query and metadata
            
        Returns:
            AgentResponse: Standardized response from the agent
        """
        pass
    
    async def run(
        self,
        context: AgentContext,
        current_user: Optional[UserTenantDB] = None
    ) -> AgentResponse:
        """
        Main entry point for running the agent.
        
        Args:
            context: The context containing user query and metadata
            current_user: Current user information for tenant-specific operations
            
        Returns:
            AgentResponse: The agent's response
        """
        start_time = time.time()
        
        try:
            self.logger.info(f"Running agent {self.name} for query: {context.user_query[:100]}...")
            
            # Create config for LangGraph execution
            config = {
                "configurable": {
                    "thread_id": context.session_id or f"{context.user_id}_{int(time.time())}",
                    "user_id": context.user_id,
                    "tenant_id": context.tenant_id
                }
            }
            
            # Create input message
            input_message = HumanMessage(content=context.user_query)
            
            # Run with timeout
            result = await asyncio.wait_for(
                self.graph.ainvoke(
                    {"messages": [input_message]},
                    config=config
                ),
                timeout=self.timeout_seconds
            )
            
            # Extract response from the result
            messages = result.get("messages", [])
            if not messages:
                raise AgentError("No response generated", self.name, "output")
            
            last_message = messages[-1]
            if not isinstance(last_message, AIMessage):
                raise AgentError("Invalid response format", self.name, "output")
            
            # Calculate processing time
            processing_time = (time.time() - start_time) * 1000
            
            # Extract response data from additional_kwargs
            kwargs = last_message.additional_kwargs
            
            return AgentResponse(
                agent_type=kwargs.get("agent_type", self.agent_type),
                agent_name=kwargs.get("agent_name", self.name),
                status=kwargs.get("status", AgentStatus.SUCCESS),
                content=last_message.content,
                sources=kwargs.get("sources", []),
                metadata=kwargs.get("metadata", {}),
                processing_time_ms=processing_time,
                error_message=kwargs.get("error_message")
            )
            
        except asyncio.TimeoutError:
            self.logger.error(f"Agent {self.name} timed out after {self.timeout_seconds} seconds")
            return AgentResponse(
                agent_type=self.agent_type,
                agent_name=self.name,
                status=AgentStatus.TIMEOUT,
                content="माफ गर्नुहोस्, प्रतिक्रिया दिन धेरै समय लाग्यो। कृपया फेरि प्रयास गर्नुहोस्।",
                processing_time_ms=(time.time() - start_time) * 1000,
                error_message="Agent execution timed out"
            )
            
        except Exception as e:
            self.logger.error(f"Error running agent {self.name}: {e}")
            return AgentResponse(
                agent_type=self.agent_type,
                agent_name=self.name,
                status=AgentStatus.ERROR,
                content="माफ गर्नुहोस्, केही समस्या भयो। कृपया फेरि प्रयास गर्नुहोस्।",
                processing_time_ms=(time.time() - start_time) * 1000,
                error_message=str(e)
            )
    
    def get_info(self) -> Dict[str, Any]:
        """Get information about this agent."""
        return {
            "name": self.name,
            "type": self.agent_type,
            "timeout_seconds": self.timeout_seconds,
            "max_retries": self.max_retries,
            "status": "active"
        }
