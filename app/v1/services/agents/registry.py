# app/v1/services/agents/registry.py

from typing import Dict, List, Optional, Type
from app.shared.utils.logger import setup_new_logging
from .base_agent import BaseAgent
from .types import AgentType

logger = setup_new_logging(__name__)

class AgentRegistry:
    """
    Registry for managing all available agents in the system.
    
    Provides functionality for:
    - Registering new agents
    - Retrieving agents by name or type
    - Listing available agents
    - Managing agent lifecycle
    """
    
    def __init__(self):
        self._agents: Dict[str, BaseAgent] = {}
        self._agent_types: Dict[AgentType, List[str]] = {}
        self.logger = setup_new_logging(f"{__name__}.AgentRegistry")
    
    def register_agent(self, agent: BaseAgent) -> None:
        """
        Register a new agent in the registry.
        
        Args:
            agent: The agent instance to register
        """
        try:
            if agent.name in self._agents:
                self.logger.warning(f"Agent {agent.name} already registered, overwriting")
            
            self._agents[agent.name] = agent
            
            # Update type mapping
            if agent.agent_type not in self._agent_types:
                self._agent_types[agent.agent_type] = []
            
            if agent.name not in self._agent_types[agent.agent_type]:
                self._agent_types[agent.agent_type].append(agent.name)
            
            self.logger.info(f"Registered agent: {agent.name} (type: {agent.agent_type})")
            
        except Exception as e:
            self.logger.error(f"Failed to register agent {agent.name}: {e}")
            raise
    
    def get_agent(self, name: str) -> Optional[BaseAgent]:
        """
        Get an agent by name.
        
        Args:
            name: The name of the agent
            
        Returns:
            The agent instance or None if not found
        """
        return self._agents.get(name)
    
    def get_agents_by_type(self, agent_type: AgentType) -> List[BaseAgent]:
        """
        Get all agents of a specific type.
        
        Args:
            agent_type: The type of agents to retrieve
            
        Returns:
            List of agent instances of the specified type
        """
        agent_names = self._agent_types.get(agent_type, [])
        return [self._agents[name] for name in agent_names if name in self._agents]
    
    def list_agents(self) -> List[Dict[str, str]]:
        """
        List all registered agents with their basic information.
        
        Returns:
            List of dictionaries containing agent information
        """
        return [
            {
                "name": agent.name,
                "type": agent.agent_type,
                "status": "active"
            }
            for agent in self._agents.values()
        ]
    
    def get_agent_info(self, name: str) -> Optional[Dict]:
        """
        Get detailed information about a specific agent.
        
        Args:
            name: The name of the agent
            
        Returns:
            Dictionary containing agent information or None if not found
        """
        agent = self.get_agent(name)
        return agent.get_info() if agent else None
    
    def unregister_agent(self, name: str) -> bool:
        """
        Unregister an agent from the registry.
        
        Args:
            name: The name of the agent to unregister
            
        Returns:
            True if agent was unregistered, False if not found
        """
        if name not in self._agents:
            return False
        
        agent = self._agents[name]
        
        # Remove from type mapping
        if agent.agent_type in self._agent_types:
            if name in self._agent_types[agent.agent_type]:
                self._agent_types[agent.agent_type].remove(name)
            
            # Clean up empty type lists
            if not self._agent_types[agent.agent_type]:
                del self._agent_types[agent.agent_type]
        
        # Remove from main registry
        del self._agents[name]
        
        self.logger.info(f"Unregistered agent: {name}")
        return True
    
    def is_registered(self, name: str) -> bool:
        """
        Check if an agent is registered.
        
        Args:
            name: The name of the agent
            
        Returns:
            True if agent is registered, False otherwise
        """
        return name in self._agents
    
    def get_available_types(self) -> List[AgentType]:
        """
        Get all available agent types.
        
        Returns:
            List of available agent types
        """
        return list(self._agent_types.keys())
    
    def health_check(self) -> Dict[str, any]:
        """
        Perform a health check on all registered agents.
        
        Returns:
            Dictionary containing health status of all agents
        """
        health_status = {
            "total_agents": len(self._agents),
            "agents_by_type": {
                agent_type.value: len(agents) 
                for agent_type, agents in self._agent_types.items()
            },
            "agents": {}
        }
        
        for name, agent in self._agents.items():
            try:
                # Basic health check - ensure agent is properly initialized
                agent_health = {
                    "status": "healthy" if agent.graph is not None else "unhealthy",
                    "type": agent.agent_type,
                    "timeout": agent.timeout_seconds,
                    "max_retries": agent.max_retries
                }
                health_status["agents"][name] = agent_health
            except Exception as e:
                health_status["agents"][name] = {
                    "status": "error",
                    "error": str(e)
                }
        
        return health_status

# Global registry instance
agent_registry = AgentRegistry()
