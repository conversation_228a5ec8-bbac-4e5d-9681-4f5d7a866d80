# app/v1/services/agents/health.py

import asyncio
import time
from typing import Dict, Any, List
from datetime import datetime

from app.shared.utils.logger import setup_new_logging
from app.shared.config.agent_config import agent_config
from .registry import agent_registry
from .memory.mongo_memory import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MongoMemoryStore
from .memory.session_memory import SessionMemoryManager
from .memory.user_context import UserContextManager

logger = setup_new_logging(__name__)

class AgentHealthChecker:
    """
    Comprehensive health checker for the agent system.
    
    Monitors:
    - Agent availability and responsiveness
    - Memory system health
    - Database connectivity
    - System performance metrics
    """
    
    def __init__(self):
        self.logger = setup_new_logging(f"{__name__}.AgentHealthChecker")
    
    async def comprehensive_health_check(self) -> Dict[str, Any]:
        """
        Perform a comprehensive health check of all system components.
        
        Returns:
            Dictionary containing detailed health information
        """
        start_time = time.time()
        
        health_data = {
            "timestamp": datetime.utcnow().isoformat(),
            "system_status": "unknown",
            "components": {},
            "performance": {},
            "errors": []
        }
        
        try:
            # Check agent registry
            registry_health = await self._check_agent_registry()
            health_data["components"]["agent_registry"] = registry_health
            
            # Check memory systems
            memory_health = await self._check_memory_systems()
            health_data["components"]["memory"] = memory_health
            
            # Check database connectivity
            db_health = await self._check_database_connectivity()
            health_data["components"]["database"] = db_health
            
            # Check agent responsiveness
            agent_health = await self._check_agent_responsiveness()
            health_data["components"]["agents"] = agent_health
            
            # Calculate overall system status
            health_data["system_status"] = self._calculate_overall_status(health_data["components"])
            
            # Performance metrics
            health_data["performance"] = {
                "health_check_duration_ms": (time.time() - start_time) * 1000,
                "total_agents": registry_health.get("total_agents", 0),
                "active_agents": len([a for a in agent_health.get("agents", {}).values() if a.get("status") == "healthy"])
            }
            
        except Exception as e:
            self.logger.error(f"Health check failed: {e}")
            health_data["system_status"] = "error"
            health_data["errors"].append(f"Health check error: {str(e)}")
        
        return health_data
    
    async def _check_agent_registry(self) -> Dict[str, Any]:
        """Check the health of the agent registry."""
        try:
            registry_health = agent_registry.health_check()
            
            return {
                "status": "healthy" if registry_health["total_agents"] > 0 else "unhealthy",
                "total_agents": registry_health["total_agents"],
                "agents_by_type": registry_health["agents_by_type"],
                "details": registry_health
            }
            
        except Exception as e:
            self.logger.error(f"Agent registry health check failed: {e}")
            return {
                "status": "error",
                "error": str(e),
                "total_agents": 0
            }
    
    async def _check_memory_systems(self) -> Dict[str, Any]:
        """Check the health of memory systems."""
        memory_health = {
            "status": "healthy",
            "components": {}
        }
        
        # Check session memory
        try:
            session_manager = SessionMemoryManager()
            test_stats = session_manager.get_session_stats()
            memory_health["components"]["session_memory"] = {
                "status": "healthy",
                "stats": test_stats
            }
        except Exception as e:
            self.logger.error(f"Session memory health check failed: {e}")
            memory_health["components"]["session_memory"] = {
                "status": "error",
                "error": str(e)
            }
            memory_health["status"] = "degraded"
        
        # Check user context
        try:
            context_manager = UserContextManager()
            test_context = context_manager.get_user_context("health_check", "test")
            memory_health["components"]["user_context"] = {
                "status": "healthy",
                "test_passed": True
            }
        except Exception as e:
            self.logger.error(f"User context health check failed: {e}")
            memory_health["components"]["user_context"] = {
                "status": "error",
                "error": str(e)
            }
            memory_health["status"] = "degraded"
        
        return memory_health
    
    async def _check_database_connectivity(self) -> Dict[str, Any]:
        """Check database connectivity."""
        db_health = {
            "status": "healthy",
            "connections": {}
        }
        
        # Check MongoDB connectivity
        try:
            checkpointer = MongoDBCheckpointer()
            # Try a simple operation
            test_config = {
                "configurable": {
                    "thread_id": "health_check",
                    "tenant_id": "test"
                }
            }
            checkpointer.get_tuple(test_config)  # This will return None but tests connectivity
            
            db_health["connections"]["mongodb_checkpointer"] = {
                "status": "healthy",
                "uri": agent_config.memory_mongo_uri.split("@")[-1] if "@" in agent_config.memory_mongo_uri else "localhost"
            }
        except Exception as e:
            self.logger.error(f"MongoDB checkpointer connectivity failed: {e}")
            db_health["connections"]["mongodb_checkpointer"] = {
                "status": "error",
                "error": str(e)
            }
            db_health["status"] = "degraded"
        
        # Check memory store
        try:
            memory_store = MongoMemoryStore()
            test_value = memory_store.get(("health_check",), "test", "test")
            
            db_health["connections"]["mongodb_memory_store"] = {
                "status": "healthy"
            }
        except Exception as e:
            self.logger.error(f"MongoDB memory store connectivity failed: {e}")
            db_health["connections"]["mongodb_memory_store"] = {
                "status": "error",
                "error": str(e)
            }
            db_health["status"] = "degraded"
        
        return db_health
    
    async def _check_agent_responsiveness(self) -> Dict[str, Any]:
        """Check if agents are responsive."""
        agent_health = {
            "status": "healthy",
            "agents": {}
        }
        
        # Test each registered agent
        for agent_name in agent_registry._agents.keys():
            try:
                agent = agent_registry.get_agent(agent_name)
                if agent:
                    # Simple health check - verify agent is properly initialized
                    agent_info = agent.get_info()
                    
                    agent_health["agents"][agent_name] = {
                        "status": "healthy" if agent.graph is not None else "unhealthy",
                        "type": agent.agent_type,
                        "timeout": agent.timeout_seconds,
                        "info": agent_info
                    }
                else:
                    agent_health["agents"][agent_name] = {
                        "status": "error",
                        "error": "Agent not found in registry"
                    }
                    
            except Exception as e:
                self.logger.error(f"Agent {agent_name} health check failed: {e}")
                agent_health["agents"][agent_name] = {
                    "status": "error",
                    "error": str(e)
                }
        
        # Check if any agents are unhealthy
        unhealthy_agents = [
            name for name, health in agent_health["agents"].items() 
            if health.get("status") != "healthy"
        ]
        
        if unhealthy_agents:
            agent_health["status"] = "degraded"
            agent_health["unhealthy_agents"] = unhealthy_agents
        
        return agent_health
    
    def _calculate_overall_status(self, components: Dict[str, Any]) -> str:
        """Calculate overall system status based on component health."""
        statuses = []
        
        for component_name, component_health in components.items():
            status = component_health.get("status", "unknown")
            statuses.append(status)
        
        # Determine overall status
        if all(status == "healthy" for status in statuses):
            return "healthy"
        elif any(status == "error" for status in statuses):
            return "unhealthy"
        elif any(status == "degraded" for status in statuses):
            return "degraded"
        else:
            return "unknown"
    
    async def quick_health_check(self) -> Dict[str, Any]:
        """
        Perform a quick health check for monitoring endpoints.
        
        Returns:
            Basic health information
        """
        try:
            registry_health = agent_registry.health_check()
            
            return {
                "status": "healthy" if registry_health["total_agents"] > 0 else "unhealthy",
                "timestamp": datetime.utcnow().isoformat(),
                "total_agents": registry_health["total_agents"],
                "agent_mode_enabled": agent_config.AGENT_MODE_ENABLED
            }
            
        except Exception as e:
            return {
                "status": "error",
                "timestamp": datetime.utcnow().isoformat(),
                "error": str(e),
                "total_agents": 0,
                "agent_mode_enabled": False
            }

# Global health checker instance
health_checker = AgentHealthChecker()
