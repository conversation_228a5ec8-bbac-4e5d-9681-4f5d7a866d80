# app/v1/services/agents/routes.py

from fastapi import APIRouter, Depends, HTTPException, Query
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field

from app.shared.database.models import UserTenantDB
from app.shared.security.dependencies import get_current_user
from app.shared.utils.logger import setup_new_logging
from app.shared.config.agent_config import agent_config

from .service import AgentService
from .types import AgentResponse, AgentContext
from app.v1.services.chat.models import UniformResponse

router = APIRouter(prefix="/agents", tags=["Multi-Agent System"])
logger = setup_new_logging(__name__)

# Request/Response Models
class AgentChatRequest(BaseModel):
    """Request model for agent chat."""
    query: str = Field(..., description="User query to process")
    conversation_id: Optional[str] = Field(None, description="Conversation ID for context")
    session_id: Optional[str] = Field(None, description="Session ID for memory")
    language: str = Field("nepali", description="Language preference")
    max_results: int = Field(5, ge=1, le=20, description="Maximum number of results")
    include_sources: bool = Field(True, description="Whether to include source references")
    agent_name: Optional[str] = Field(None, description="Specific agent to use (optional)")

class AgentChatResponse(BaseModel):
    """Response model for agent chat."""
    agent_type: str
    agent_name: str
    status: str
    content: str
    sources: List[Dict[str, Any]] = Field(default_factory=list)  # Legacy format
    uniform_sources: Optional[UniformResponse] = Field(None, description="New uniform response format")
    metadata: Dict[str, Any] = Field(default_factory=dict)
    processing_time_ms: Optional[float] = None
    conversation_id: Optional[str] = None
    session_id: Optional[str] = None

class AgentHealthResponse(BaseModel):
    """Response model for agent health check."""
    system_status: str
    total_agents: int
    agents_by_type: Dict[str, int]
    agents: Dict[str, Dict[str, Any]]
    memory_status: Dict[str, Any]

class AgentListResponse(BaseModel):
    """Response model for listing agents."""
    agents: List[Dict[str, str]]
    total_count: int

# Routes
@router.post("/chat", response_model=AgentChatResponse)
async def agent_chat(
    request: AgentChatRequest,
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Process a chat query using the multi-agent system.
    
    The orchestrator will analyze the query and route it to the most appropriate agent.
    """
    if not agent_config.AGENT_MODE_ENABLED:
        raise HTTPException(
            status_code=503,
            detail="Agent system is currently disabled"
        )
    
    try:
        logger.info(f"Processing agent chat request from user {current_user.id}")
        
        # Create agent context
        context = AgentContext(
            user_id=current_user.id,
            tenant_id=current_user.tenant_id,
            conversation_id=request.conversation_id,
            session_id=request.session_id,
            user_query=request.query,
            language=request.language,
            max_results=request.max_results,
            include_sources=request.include_sources
        )
        
        # Process through agent service
        agent_service = AgentService()
        
        if request.agent_name:
            # Use specific agent if requested
            response = await agent_service.run_specific_agent(
                agent_name=request.agent_name,
                context=context,
                current_user=current_user
            )
        else:
            # Use orchestrator for routing
            response = await agent_service.process_query(context, current_user)
        
        return AgentChatResponse(
            agent_type=response.agent_type,
            agent_name=response.agent_name,
            status=response.status,
            content=response.content,
            sources=[source.dict() if hasattr(source, 'dict') else source for source in response.sources],
            uniform_sources=response.uniform_sources,
            metadata=response.metadata,
            processing_time_ms=response.processing_time_ms,
            conversation_id=context.conversation_id,
            session_id=context.session_id
        )
        
    except Exception as e:
        logger.error(f"Error in agent chat: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Agent processing failed: {str(e)}"
        )

@router.get("/health", response_model=AgentHealthResponse)
async def agent_health(
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Get health status of the agent system.
    """
    try:
        agent_service = AgentService()
        health_data = await agent_service.health_check()
        
        return AgentHealthResponse(
            system_status=health_data["system_status"],
            total_agents=health_data["total_agents"],
            agents_by_type=health_data["agents_by_type"],
            agents=health_data["agents"],
            memory_status=health_data["memory_status"]
        )
        
    except Exception as e:
        logger.error(f"Error in agent health check: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Health check failed: {str(e)}"
        )

@router.get("/list", response_model=AgentListResponse)
async def list_agents(
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    List all available agents in the system.
    """
    try:
        agent_service = AgentService()
        agents = agent_service.list_agents()
        
        return AgentListResponse(
            agents=agents,
            total_count=len(agents)
        )
        
    except Exception as e:
        logger.error(f"Error listing agents: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list agents: {str(e)}"
        )

@router.get("/info/{agent_name}")
async def get_agent_info(
    agent_name: str,
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Get detailed information about a specific agent.
    """
    try:
        agent_service = AgentService()
        agent_info = agent_service.get_agent_info(agent_name)
        
        if not agent_info:
            raise HTTPException(
                status_code=404,
                detail=f"Agent '{agent_name}' not found"
            )
        
        return agent_info
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting agent info: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get agent info: {str(e)}"
        )

@router.post("/memory/session")
async def create_session(
    conversation_id: Optional[str] = None,
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Create a new agent session for memory management.
    """
    try:
        agent_service = AgentService()
        session_id = await agent_service.create_session(
            user_id=current_user.id,
            tenant_id=current_user.tenant_id,
            conversation_id=conversation_id
        )
        
        return {"session_id": session_id}
        
    except Exception as e:
        logger.error(f"Error creating session: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create session: {str(e)}"
        )

@router.get("/memory/sessions")
async def get_user_sessions(
    limit: int = Query(10, ge=1, le=50),
    active_only: bool = Query(True),
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Get user's agent sessions.
    """
    try:
        agent_service = AgentService()
        sessions = await agent_service.get_user_sessions(
            user_id=current_user.id,
            tenant_id=current_user.tenant_id,
            limit=limit,
            active_only=active_only
        )
        
        return {"sessions": sessions}
        
    except Exception as e:
        logger.error(f"Error getting user sessions: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get sessions: {str(e)}"
        )

@router.delete("/memory/session/{session_id}")
async def deactivate_session(
    session_id: str,
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Deactivate an agent session.
    """
    try:
        agent_service = AgentService()
        success = await agent_service.deactivate_session(session_id)
        
        if not success:
            raise HTTPException(
                status_code=404,
                detail="Session not found or already deactivated"
            )
        
        return {"message": "Session deactivated successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deactivating session: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to deactivate session: {str(e)}"
        )

@router.get("/memory/context")
async def get_user_context(
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Get user's context and preferences.
    """
    try:
        agent_service = AgentService()
        context = await agent_service.get_user_context(
            user_id=current_user.id,
            tenant_id=current_user.tenant_id
        )
        
        return context
        
    except Exception as e:
        logger.error(f"Error getting user context: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get user context: {str(e)}"
        )

@router.get("/stats")
async def get_agent_stats(
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Get agent system statistics for the current tenant.
    """
    try:
        agent_service = AgentService()
        stats = await agent_service.get_tenant_stats(current_user.tenant_id)

        return stats

    except Exception as e:
        logger.error(f"Error getting agent stats: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get stats: {str(e)}"
        )

@router.get("/tools")
async def get_available_tools(
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Get information about available tools and their capabilities.
    """
    try:
        agent_service = AgentService()

        # Get main agent to access tool information
        main_agent = agent_service.agent_registry.get_agent("main_agent")
        if not main_agent:
            raise HTTPException(
                status_code=503,
                detail="Main agent not available"
            )

        tools_info = main_agent.get_tool_selection_info()
        available_tools = main_agent.get_available_tools()

        return {
            "available_tools": available_tools,
            "tool_capabilities": tools_info,
            "selection_method": "llm_powered",
            "supports_multi_tool": True
        }

    except Exception as e:
        logger.error(f"Error getting tools info: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get tools info: {str(e)}"
        )

@router.post("/explain-selection")
async def explain_tool_selection(
    request: AgentChatRequest,
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Explain why specific tools would be selected for a given query without executing them.
    """
    try:
        agent_service = AgentService()

        # Get main agent
        main_agent = agent_service.agent_registry.get_agent("main_agent")
        if not main_agent:
            raise HTTPException(
                status_code=503,
                detail="Main agent not available"
            )

        # Get tool selection decision
        tool_decision = await main_agent.tool_selector.select_tools(
            request.query,
            None,  # No conversation context for explanation
            None   # No user preferences for explanation
        )

        # Get explanation
        explanation = await main_agent.tool_selector.explain_tool_selection(
            request.query,
            tool_decision
        )

        return {
            "query": request.query,
            "selected_tools": tool_decision.selected_tools,
            "reasoning": tool_decision.reasoning,
            "confidence": tool_decision.confidence,
            "explanation": explanation,
            "priority_order": tool_decision.priority_order
        }

    except Exception as e:
        logger.error(f"Error explaining tool selection: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to explain tool selection: {str(e)}"
        )

@router.get("/conversation/{conversation_id}/summary")
async def get_conversation_summary(
    conversation_id: str,
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Get a summary of a specific conversation.
    """
    try:
        agent_service = AgentService()

        # Get main agent
        main_agent = agent_service.agent_registry.get_agent("main_agent")
        if not main_agent:
            raise HTTPException(
                status_code=503,
                detail="Main agent not available"
            )

        # Create context for summary
        context = AgentContext(
            user_id=current_user.id,
            tenant_id=current_user.tenant_id,
            conversation_id=conversation_id,
            user_query="",  # Not needed for summary
            language="nepali"
        )

        summary = await main_agent.get_conversation_summary(context)

        if not summary:
            raise HTTPException(
                status_code=404,
                detail="Conversation not found or has no content"
            )

        return {
            "conversation_id": conversation_id,
            "summary": summary
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting conversation summary: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get conversation summary: {str(e)}"
        )
