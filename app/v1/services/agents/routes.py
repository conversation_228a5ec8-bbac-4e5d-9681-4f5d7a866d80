# app/v1/services/agents/routes.py

from fastapi import APIRouter, Depends, HTTPException
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field

from app.shared.database.models import UserTenantDB
from app.shared.security.dependencies import get_current_user
from app.shared.utils.logger import setup_new_logging
from app.shared.config.agent_config import agent_config

from .service import AgentService
from .types import AgentResponse, AgentContext

router = APIRouter(prefix="/agents", tags=["Multi-Agent System"])
logger = setup_new_logging(__name__)

# Request/Response Models
class AgentChatRequest(BaseModel):
    """Request model for agent chat."""
    query: str = Field(..., description="User query to process")
    conversation_history: Optional[List[Dict[str, str]]] = Field(default_factory=list, description="Previous conversation messages")
    session_id: Optional[str] = Field(None, description="Session ID for memory")
    language: str = Field("nepali", description="Language preference")
    max_results: int = Field(5, ge=1, le=20, description="Maximum number of results")
    include_sources: bool = Field(True, description="Whether to include source references")

class AgentChatResponse(BaseModel):
    """Response model for agent chat."""
    content: str
    sources: List[Dict[str, Any]] = Field(default_factory=list)
    processing_time_ms: Optional[float] = None
    session_id: Optional[str] = None

# Essential Routes Only
@router.post("/chat", response_model=AgentChatResponse)
async def agent_chat(
    request: AgentChatRequest,
    current_user: UserTenantDB = Depends(get_current_user)
):
    """
    Process a chat query using the multi-agent system.
    This is the main endpoint used by the chat history service.
    """
    if not agent_config.AGENT_MODE_ENABLED:
        raise HTTPException(
            status_code=503,
            detail="Agent system is currently disabled"
        )
    
    try:
        logger.info(f"Processing agent chat request from user {current_user.id}")
        
        # Create agent context
        context = AgentContext(
            user_id=current_user.id,
            tenant_id=current_user.tenant_id,
            query=request.query,
            conversation_history=request.conversation_history,
            session_id=request.session_id,
            language=request.language,
            max_results=request.max_results,
            include_sources=request.include_sources
        )
        
        # Process through agent service
        agent_service = AgentService()
        response = await agent_service.process_query(context, current_user)
        
        return AgentChatResponse(
            content=response.content,
            sources=response.sources if response.sources else [],
            processing_time_ms=response.processing_time_ms,
            session_id=context.session_id
        )
        
    except Exception as e:
        logger.error(f"Error in agent chat: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Agent processing failed: {str(e)}"
        )
