# app/v1/services/agents/document_search_agent.py

import time
from typing import List
from app.shared.database.models import UserTenantDB
from app.v1.services.knowledge_base.service import KnowledgeBaseService
from .base_agent import BaseAgent
from .types import AgentResponse, AgentContext, AgentType, AgentStatus, SourceReference

class DocumentSearchAgent(BaseAgent):
    """
    Simple document search agent - just search and return results.
    """

    def __init__(self):
        super().__init__(
            name="document_search",
            agent_type=AgentType.DOCUMENT_SEARCH,
            timeout_seconds=30,
            max_retries=1
        )
    
    async def _process_query(self, context: AgentContext, current_user: UserTenantDB) -> AgentResponse:
        """
        Document search that returns raw source nodes without grouping.
        Grouping will be handled after agent execution during response formatting.
        """
        start_time = time.time()

        try:
            # Use KnowledgeBaseService directly for search
            kb_service = KnowledgeBaseService(current_user)

            # Get raw source nodes (not grouped)
            self.logger.info(f"Searching for: {context.user_query}")
            raw_source_nodes = await kb_service.retrieve_source_nodes(
                context.user_query,
                context.max_results or 10  # Get more raw nodes for better selection
            )
            self.logger.info(f"Search returned {len(raw_source_nodes)} raw source nodes")

            # Convert raw source nodes to legacy sources format
            sources = []
            for node in raw_source_nodes:
                # Extract metadata from the source node
                metadata = getattr(node, 'metadata', {})
                node_content = getattr(node, 'text', '') or getattr(node, 'content', '')

                sources.append(SourceReference(
                    document_id=metadata.get('sent_id', '') or metadata.get('id', ''),
                    filename=metadata.get('source_name', '') or metadata.get('filename', 'unknown.pdf'),
                    page_number=metadata.get('page_number', 0),
                    chunk_id=metadata.get('sent_id', '') or metadata.get('chunk_id', ''),
                    text_snippet=node_content[:300] + "..." if len(node_content) > 300 else node_content,
                    confidence_score=getattr(node, 'score', 0.0)
                ))

            processing_time = (time.time() - start_time) * 1000

            # Generate a simple response based on search results
            if sources:
                content = f"तपाईंको खोज \"{context.user_query}\" को लागि {len(sources)} वटा सम्बन्धित दस्तावेज खण्डहरू फेला परेका छन्।"
            else:
                content = f"माफ गर्नुहोस्, तपाईंको खोज \"{context.user_query}\" को लागि कुनै सम्बन्धित दस्तावेज फेला परेन।"

            # Create uniform response format with raw sources (ungrouped)
            from app.v1.services.chat.models import UniformResponse

            # For now, return raw sources without grouping
            # The main agent will handle grouping during response synthesis
            uniform_sources = UniformResponse(
                sources=[],  # Will be populated by main agent during synthesis
                total_sources_found=len(sources)
            )

            return AgentResponse(
                agent_type=self.agent_type,
                agent_name=self.name,
                status=AgentStatus.SUCCESS,
                content=content,
                sources=sources,
                uniform_sources=uniform_sources,
                metadata={
                    "total_sources_found": len(sources),
                    "search_type": "vector_search",
                    "raw_nodes": True,  # Indicates these are raw, ungrouped nodes
                    "query": context.user_query
                },
                processing_time_ms=processing_time
            )

        except Exception as e:
            self.logger.error(f"Error in document search agent: {e}")

            processing_time = (time.time() - start_time) * 1000
            return AgentResponse(
                agent_type=self.agent_type,
                agent_name=self.name,
                status=AgentStatus.ERROR,
                content="माफ गर्नुहोस्, दस्तावेज खोज्दा समस्या भयो। कृपया फेरि प्रयास गर्नुहोस्।",
                processing_time_ms=processing_time,
                error_message=str(e)
            )

    def get_capabilities(self) -> List[str]:
        """Get list of capabilities this agent provides."""
        return [
            "document_search",
            "vector_search"
        ]
