# app/v1/services/agents/main_agent.py

import time
import json
from typing import Dict, Any, List, Optional, Literal
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.tools import tool
from langchain_openai import ChatOpenAI
from langgraph.types import Command
from langgraph.prebuilt import ToolNode

from app.shared.database.models import UserTenantDB
from app.shared.config.agent_config import agent_config
from app.shared.utils.logger import setup_new_logging
from .base_agent import BaseAgent
from .types import AgentR<PERSON>ponse, AgentContext, AgentType, AgentStatus
from .registry import agent_registry
from .memory.mongo_memory import <PERSON>go<PERSON><PERSON>he<PERSON>pointer, MongoMemoryStore
from .memory.context_manager import ContextManager
from .response_synthesis import ResponseSynthesisEngine
from .tool_selection import ToolSelector

logger = setup_new_logging(__name__)

class MainAgent(BaseAgent):
    """
    LLM-powered main agent that dynamically determines which sub-agents or tools to invoke.
    
    This agent replaces the hardcoded orchestrator routing logic with intelligent LLM-based
    decision making. It:
    
    1. Analyzes user queries using LLM reasoning
    2. Dynamically selects appropriate tools/sub-agents
    3. Manages conversation context and memory
    4. Synthesizes comprehensive responses
    5. Handles source node grouping and formatting
    """
    
    def __init__(self):
        super().__init__(
            name="main_agent",
            agent_type=AgentType.MAIN_AGENT,
            timeout_seconds=agent_config.AGENT_DEFAULT_TIMEOUT + 20,  # Extra time for LLM processing
            max_retries=agent_config.AGENT_MAX_RETRIES
        )
        
        # Initialize LLM for decision making
        self.llm = ChatOpenAI(
            model=agent_config.AGENT_LLM_MODEL,
            temperature=agent_config.AGENT_LLM_TEMPERATURE,
            max_tokens=agent_config.AGENT_LLM_MAX_TOKENS
        )
        
        # Initialize memory components
        self.memory_store = MongoMemoryStore()
        self.checkpointer = MongoDBCheckpointer()
        self.context_manager = ContextManager(self.memory_store)

        # Initialize response synthesis engine
        self.synthesis_engine = ResponseSynthesisEngine()

        # Initialize tool selector
        self.tool_selector = ToolSelector()

        # System prompt for the main agent
        self.system_prompt = self._create_system_prompt()

        # Available tools/sub-agents
        self.available_tools = self._initialize_tools()
        
        logger.info(f"Initialized MainAgent with {len(self.available_tools)} available tools")
    
    def _create_system_prompt(self) -> str:
        """Create the system prompt for the main agent."""
        return f"""You are a legal assistant main agent responsible for intelligently routing user queries and synthesizing responses.

Your role:
1. Analyze user queries to understand intent and requirements
2. Select the most appropriate tool(s) or sub-agent(s) to handle the query
3. Coordinate multiple tool calls if needed
4. Synthesize comprehensive responses from tool outputs
5. Maintain conversation context and user preferences

Available tools:
- document_search: Search legal documents and retrieve relevant information
- general_chat: Handle general legal questions and conversations
- analysis: Perform legal analysis and interpretation
- data_retrieval: Retrieve specific data or information

Guidelines:
- Always prioritize accuracy and relevance
- Use document_search for queries requiring specific legal document information
- Use general_chat for conversational queries or general legal advice
- Combine multiple tools when needed for comprehensive answers
- Maintain context across conversation turns
- Respond in Nepali language unless specifically requested otherwise
- Always provide source references when using document search results

Current conversation context will be provided with each query.
"""
    
    def _initialize_tools(self) -> List[Any]:
        """Initialize available tools for the main agent."""
        tools = []
        
        # Document search tool
        @tool
        def search_documents(query: str, max_results: int = 5) -> Dict[str, Any]:
            """Search legal documents for relevant information.
            
            Args:
                query: The search query
                max_results: Maximum number of results to return
                
            Returns:
                Dictionary containing search results and metadata
            """
            return Command(
                goto="document_search_agent",
                update={
                    "tool_query": query,
                    "tool_max_results": max_results,
                    "tool_name": "search_documents"
                }
            )
        
        # General chat tool
        @tool
        def general_chat(query: str, context: Optional[str] = None) -> Dict[str, Any]:
            """Handle general legal questions and conversations.
            
            Args:
                query: The user query
                context: Additional context for the conversation
                
            Returns:
                Dictionary containing chat response and metadata
            """
            return Command(
                goto="general_chat_agent",
                update={
                    "tool_query": query,
                    "tool_context": context,
                    "tool_name": "general_chat"
                }
            )
        
        # Analysis tool
        @tool
        def analyze_legal_content(content: str, analysis_type: str = "general") -> Dict[str, Any]:
            """Perform legal analysis on provided content.
            
            Args:
                content: The content to analyze
                analysis_type: Type of analysis (general, contract, statute, etc.)
                
            Returns:
                Dictionary containing analysis results
            """
            return Command(
                goto="analysis_agent",
                update={
                    "tool_content": content,
                    "tool_analysis_type": analysis_type,
                    "tool_name": "analyze_legal_content"
                }
            )
        
        tools.extend([search_documents, general_chat, analyze_legal_content])
        return tools
    
    async def _process_query(self, context: AgentContext, current_user: UserTenantDB) -> AgentResponse:
        """
        Process a query using LLM-powered tool selection and response synthesis.
        """
        start_time = time.time()
        
        try:
            # Load conversation context and memory using context manager
            conversation_context = await self.context_manager.load_conversation_context(
                context.user_id, context.tenant_id, context.conversation_id, context.session_id
            )
            
            # Create messages for LLM decision making
            messages = await self._prepare_messages(context, conversation_context)
            
            # Get intelligent tool selection using the tool selector
            tool_decision = await self.tool_selector.select_tools(
                context.user_query,
                conversation_context,
                conversation_context.get("user_profile", {})
            )
            
            # Execute selected tool(s)
            tool_results = await self._execute_tools(tool_decision, context, current_user)
            
            # Synthesize final response using the synthesis engine
            final_response = await self.synthesis_engine.synthesize_response(
                context.user_query, tool_results, conversation_context
            )
            
            # Update memory with new interaction using context manager
            await self.context_manager.update_conversation_memory(
                context.user_id,
                context.tenant_id,
                conversation_context["conversation_id"],
                context.user_query,
                final_response["content"],
                tool_results,
                final_response.get("synthesis_metadata")
            )
            
            processing_time = (time.time() - start_time) * 1000
            
            return AgentResponse(
                agent_type=self.agent_type,
                agent_name=self.name,
                status=AgentStatus.SUCCESS,
                content=final_response["content"],
                uniform_sources=final_response.get("uniform_sources"),
                sources=final_response.get("sources", []),
                metadata={
                    "tool_selection": {
                        "selected_tools": tool_decision.selected_tools,
                        "reasoning": tool_decision.reasoning,
                        "confidence": tool_decision.confidence
                    },
                    "tools_used": [result.get("tool_name") for result in tool_results],
                    "conversation_turns": len(conversation_context.get("history", [])),
                    "synthesis_method": "llm_powered"
                },
                processing_time_ms=processing_time
            )
            
        except Exception as e:
            self.logger.error(f"Error in main agent processing: {e}")
            processing_time = (time.time() - start_time) * 1000
            
            return AgentResponse(
                agent_type=self.agent_type,
                agent_name=self.name,
                status=AgentStatus.ERROR,
                content="माफ गर्नुहोस्, केही समस्या भयो। कृपया फेरि प्रयास गर्नुहोस्।",
                processing_time_ms=processing_time,
                error_message=str(e)
            )
    

    
    async def _prepare_messages(self, context: AgentContext, conversation_context: Dict[str, Any]) -> List[Any]:
        """Prepare messages for LLM tool selection."""
        messages = [SystemMessage(content=self.system_prompt)]
        
        # Add conversation history (last 5 turns to manage context length)
        history = conversation_context.get("history", [])[-10:]  # Last 10 messages
        for msg in history:
            if msg.get("role") == "user":
                messages.append(HumanMessage(content=msg["content"]))
            elif msg.get("role") == "assistant":
                messages.append(AIMessage(content=msg["content"]))
        
        # Add current user query
        messages.append(HumanMessage(content=context.user_query))
        
        return messages



    async def _execute_tools(self, tool_decision, context: AgentContext, current_user: UserTenantDB) -> List[Dict[str, Any]]:
        """Execute the selected tools and return results."""
        results = []

        # Execute tools in priority order
        for tool_name in tool_decision.priority_order:
            try:
                if tool_name == "search_documents":
                    result = await self._execute_document_search(
                        {"query": context.user_query, "max_results": context.max_results or 5},
                        context, current_user
                    )
                elif tool_name == "general_chat":
                    result = await self._execute_general_chat(
                        {"query": context.user_query},
                        context, current_user
                    )
                elif tool_name == "analyze_legal_content":
                    result = await self._execute_analysis(
                        {"content": context.user_query, "analysis_type": "general"},
                        context, current_user
                    )
                else:
                    result = {"error": f"Unknown tool: {tool_name}"}

                result["tool_name"] = tool_name
                results.append(result)

            except Exception as e:
                self.logger.error(f"Error executing tool {tool_name}: {e}")
                results.append({
                    "tool_name": tool_name,
                    "error": str(e)
                })

        return results

    async def _execute_document_search(self, args: Dict[str, Any], context: AgentContext, current_user: UserTenantDB) -> Dict[str, Any]:
        """Execute document search using the document search agent."""
        try:
            # Get document search agent
            doc_agent = agent_registry.get_agent("document_search")
            if not doc_agent:
                return {"error": "Document search agent not available"}

            # Create context for document search
            search_context = AgentContext(
                user_id=context.user_id,
                tenant_id=context.tenant_id,
                conversation_id=context.conversation_id,
                session_id=context.session_id,
                user_query=args.get("query", context.user_query),
                max_results=args.get("max_results", 5),
                include_sources=True
            )

            # Execute document search
            search_response = await doc_agent.run(search_context, current_user)

            return {
                "status": search_response.status,
                "content": search_response.content,
                "sources": search_response.sources,
                "uniform_sources": search_response.uniform_sources,
                "metadata": search_response.metadata
            }

        except Exception as e:
            self.logger.error(f"Error in document search execution: {e}")
            return {"error": f"Document search failed: {str(e)}"}

    async def _execute_general_chat(self, args: Dict[str, Any], context: AgentContext, current_user: UserTenantDB) -> Dict[str, Any]:
        """Execute general chat - for now, return a simple response."""
        try:
            query = args.get("query", context.user_query)

            # Simple template-based response for general chat
            # In a full implementation, this would call a dedicated chat agent
            response_content = f"तपाईंको प्रश्न \"{query}\" को लागि सामान्य कानुनी सहायता। कृपया थप विशिष्ट प्रश्न सोध्नुहोस् वा कागजात खोज्नुहोस्।"

            return {
                "status": "success",
                "content": response_content,
                "sources": [],
                "metadata": {"response_type": "general_chat"}
            }

        except Exception as e:
            self.logger.error(f"Error in general chat execution: {e}")
            return {"error": f"General chat failed: {str(e)}"}

    async def _execute_analysis(self, args: Dict[str, Any], context: AgentContext, current_user: UserTenantDB) -> Dict[str, Any]:
        """Execute legal analysis - placeholder for future implementation."""
        try:
            content = args.get("content", "")
            analysis_type = args.get("analysis_type", "general")

            # Placeholder response for analysis
            response_content = f"कानुनी विश्लेषण ({analysis_type}): प्रदान गरिएको सामग्रीको विश्लेषण पूरा भयो। विस्तृत विश्लेषणको लागि कृपया विशेषज्ञसँग सम्पर्क गर्नुहोस्।"

            return {
                "status": "success",
                "content": response_content,
                "sources": [],
                "metadata": {"analysis_type": analysis_type, "content_length": len(content)}
            }

        except Exception as e:
            self.logger.error(f"Error in analysis execution: {e}")
            return {"error": f"Analysis failed: {str(e)}"}







    def get_capabilities(self) -> List[str]:
        """Get list of capabilities this agent provides."""
        return [
            "dynamic_tool_selection",
            "llm_powered_routing",
            "conversation_memory",
            "response_synthesis",
            "multi_tool_coordination",
            "context_awareness",
            "intelligent_tool_selection",
            "memory_consolidation",
            "source_grouping"
        ]

    def get_available_tools(self) -> List[str]:
        """Get list of available tools."""
        return list(self.tool_selector.available_tools.keys())

    def get_tool_selection_info(self) -> Dict[str, Any]:
        """Get information about tool selection capabilities."""
        return self.tool_selector.get_tool_capabilities()

    async def get_conversation_summary(self, context: AgentContext) -> Optional[str]:
        """Get a summary of the current conversation."""
        try:
            conversation_context = await self.context_manager.load_conversation_context(
                context.user_id, context.tenant_id, context.conversation_id, context.session_id
            )

            return conversation_context.get("context_summary")

        except Exception as e:
            self.logger.warning(f"Failed to get conversation summary: {e}")
            return None
