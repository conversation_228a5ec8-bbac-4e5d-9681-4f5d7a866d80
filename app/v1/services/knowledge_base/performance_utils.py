# app/v1/services/knowledge_base/performance_utils.py

import time
import asyncio
from typing import Dict, List, Optional, Callable, Any
from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)

class PerformanceMonitor:
    """
    Utility class for monitoring and comparing performance of different search methods.
    """
    
    def __init__(self):
        self.metrics = {}
        
    async def benchmark_method(
        self, 
        method_name: str, 
        method: Callable, 
        *args, 
        **kwargs
    ) -> Dict[str, Any]:
        """
        Benchmark a single method and return performance metrics.
        
        Args:
            method_name: Name of the method being benchmarked
            method: The async method to benchmark
            *args: Arguments to pass to the method
            **kwargs: Keyword arguments to pass to the method
            
        Returns:
            Dictionary containing performance metrics and results
        """
        start_time = time.time()
        start_memory = self._get_memory_usage()
        
        try:
            result = await method(*args, **kwargs)
            
            end_time = time.time()
            end_memory = self._get_memory_usage()
            
            execution_time = (end_time - start_time) * 1000  # Convert to milliseconds
            memory_delta = end_memory - start_memory
            
            # Extract result count if possible
            result_count = 0
            if isinstance(result, dict):
                if 'response' in result:
                    result_count = len(result['response'])
                elif 'results' in result:
                    result_count = len(result['results'])
            elif isinstance(result, list):
                result_count = len(result)
                
            metrics = {
                'method_name': method_name,
                'execution_time_ms': execution_time,
                'memory_delta_mb': memory_delta,
                'result_count': result_count,
                'success': True,
                'timestamp': time.time()
            }
            
            # Store metrics
            if method_name not in self.metrics:
                self.metrics[method_name] = []
            self.metrics[method_name].append(metrics)
            
            logger.info(f"Benchmark {method_name}: {execution_time:.2f}ms, {result_count} results")
            
            return {
                'metrics': metrics,
                'result': result
            }
            
        except Exception as e:
            end_time = time.time()
            execution_time = (end_time - start_time) * 1000
            
            error_metrics = {
                'method_name': method_name,
                'execution_time_ms': execution_time,
                'memory_delta_mb': 0,
                'result_count': 0,
                'success': False,
                'error': str(e),
                'timestamp': time.time()
            }
            
            if method_name not in self.metrics:
                self.metrics[method_name] = []
            self.metrics[method_name].append(error_metrics)
            
            logger.error(f"Benchmark {method_name} failed: {e}")
            
            return {
                'metrics': error_metrics,
                'result': None,
                'error': str(e)
            }
    
    async def compare_methods(
        self, 
        methods: Dict[str, Callable], 
        *args, 
        **kwargs
    ) -> Dict[str, Any]:
        """
        Compare multiple methods and return performance comparison.
        
        Args:
            methods: Dictionary of method_name -> method_callable
            *args: Arguments to pass to all methods
            **kwargs: Keyword arguments to pass to all methods
            
        Returns:
            Dictionary containing comparison results and metrics
        """
        results = {}
        
        for method_name, method in methods.items():
            benchmark_result = await self.benchmark_method(
                method_name, method, *args, **kwargs
            )
            results[method_name] = benchmark_result
            
        # Calculate comparison metrics
        successful_results = {
            name: result for name, result in results.items() 
            if result['metrics']['success']
        }
        
        if len(successful_results) > 1:
            execution_times = [
                result['metrics']['execution_time_ms'] 
                for result in successful_results.values()
            ]
            
            fastest_time = min(execution_times)
            slowest_time = max(execution_times)
            
            comparison = {
                'fastest_method': min(
                    successful_results.keys(),
                    key=lambda k: successful_results[k]['metrics']['execution_time_ms']
                ),
                'slowest_method': max(
                    successful_results.keys(),
                    key=lambda k: successful_results[k]['metrics']['execution_time_ms']
                ),
                'speed_improvement': (slowest_time - fastest_time) / slowest_time * 100,
                'fastest_time_ms': fastest_time,
                'slowest_time_ms': slowest_time
            }
            
            results['comparison'] = comparison
            
        return results
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """
        Get a summary of all performance metrics collected.
        
        Returns:
            Dictionary containing performance summary statistics
        """
        summary = {}
        
        for method_name, metrics_list in self.metrics.items():
            successful_metrics = [m for m in metrics_list if m['success']]
            
            if successful_metrics:
                execution_times = [m['execution_time_ms'] for m in successful_metrics]
                result_counts = [m['result_count'] for m in successful_metrics]
                
                summary[method_name] = {
                    'total_runs': len(metrics_list),
                    'successful_runs': len(successful_metrics),
                    'avg_execution_time_ms': sum(execution_times) / len(execution_times),
                    'min_execution_time_ms': min(execution_times),
                    'max_execution_time_ms': max(execution_times),
                    'avg_result_count': sum(result_counts) / len(result_counts) if result_counts else 0,
                    'success_rate': len(successful_metrics) / len(metrics_list) * 100
                }
            else:
                summary[method_name] = {
                    'total_runs': len(metrics_list),
                    'successful_runs': 0,
                    'success_rate': 0
                }
                
        return summary
    
    def _get_memory_usage(self) -> float:
        """
        Get current memory usage in MB.
        Returns 0 if psutil is not available.
        """
        try:
            import psutil
            import os
            process = psutil.Process(os.getpid())
            return process.memory_info().rss / 1024 / 1024  # Convert to MB
        except ImportError:
            return 0.0
    
    def reset_metrics(self):
        """Reset all collected metrics."""
        self.metrics = {}


# Global performance monitor instance
performance_monitor = PerformanceMonitor()


async def benchmark_search_methods(service, query_params: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convenience function to benchmark both search methods.
    
    Args:
        service: KnowledgeBaseService instance
        query_params: Parameters for the search methods
        
    Returns:
        Comparison results
    """
    methods = {
        'search_all_documents': service.search_all_documents,
        'search_all_documents_optimized': service.search_all_documents_optimized
    }
    
    return await performance_monitor.compare_methods(
        methods, 
        **query_params
    )


def log_performance_improvement(comparison_result: Dict[str, Any]):
    """
    Log performance improvement details.
    
    Args:
        comparison_result: Result from compare_methods
    """
    if 'comparison' in comparison_result:
        comp = comparison_result['comparison']
        logger.info(
            f"Performance Improvement: {comp['fastest_method']} is "
            f"{comp['speed_improvement']:.1f}% faster than {comp['slowest_method']} "
            f"({comp['fastest_time_ms']:.1f}ms vs {comp['slowest_time_ms']:.1f}ms)"
        )
    
    for method_name, result in comparison_result.items():
        if method_name != 'comparison' and result['metrics']['success']:
            metrics = result['metrics']
            logger.info(
                f"{method_name}: {metrics['execution_time_ms']:.1f}ms, "
                f"{metrics['result_count']} results"
            )
