# app/shared/utils/logger.py

import logging
import sys
from typing import Optional

class ColoredFormatter(logging.Formatter):
    """Custom formatter with colors for different log levels."""

    # ANSI color codes
    COLORS = {
        'DEBUG': '\033[36m',    # Cyan
        'INFO': '\033[32m',     # Green
        'WARNING': '\033[33m',  # Yellow
        'ERROR': '\033[31m',    # Red
        'CRITICAL': '\033[35m', # Magenta
    }
    RESET = '\033[0m'  # Reset color

    def format(self, record):
        # Get the color for this log level
        color = self.COLORS.get(record.levelname, self.RESET)

        # Format the message
        formatted = super().format(record)

        # Add color to the level name only
        colored_level = f"{color}{record.levelname}{self.RESET}"
        formatted = formatted.replace(record.levelname, colored_level)

        return formatted

def setup_new_logging(name: str, level: int = logging.INFO) -> logging.Logger:
    """
    Set up a new logger with the given name and level.

    Args:
        name: The name of the logger (usually __name__)
        level: The logging level (default: INFO)

    Returns:
        Configured logger instance
    """
    logger = logging.getLogger(name)

    # Avoid adding multiple handlers to the same logger
    if logger.handlers:
        return logger

    # Set level based on component - reduce noise from frequent operations
    if any(component in name for component in ['knowledge_base.service', 'agents']):
        # Only show WARNING and above for noisy components, unless DEBUG is explicitly requested
        if level == logging.DEBUG:
            logger.setLevel(logging.DEBUG)
        else:
            logger.setLevel(logging.WARNING)
    else:
        logger.setLevel(level)

    # Create console handler
    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(level)

    # Create colored formatter
    formatter = ColoredFormatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    handler.setFormatter(formatter)

    # Add handler to logger
    logger.addHandler(handler)

    return logger
