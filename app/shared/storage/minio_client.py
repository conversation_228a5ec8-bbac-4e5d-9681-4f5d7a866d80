import os
import mimetypes
from minio import Minio
from minio.error import S3Error
from pydantic import BaseModel
from typing import Optional, List
from datetime import timed<PERSON>ta
from io import BytesIO
from app.shared.utils.logger import setup_new_logging

loggers = setup_new_logging(__name__)

class MinIOConfig(BaseModel):
    minio_url: str  # Host and port, e.g., "minio.example.com:9000"
    access_key: str
    secret_key: str
    bucket_name: str
    secure: bool = True  # Use HTTPS if True


class MinIOClient:
    def __init__(self, config: MinIOConfig):
        """Initialize MinIO client and create bucket if not exists."""
        try:
            loggers.debug(f"Minio Client initialization")
            self.client = Minio(
                config.minio_url,
                access_key=config.access_key,
                secret_key=config.secret_key,
                secure=config.secure,
            )
        except Exception as e:
            loggers.debug(f"Minio Client initialization failed: {e}")
            raise Exception(f"Minio Error {e}") from e

        loggers.debug(f"Minio Client initialized")
        self.bucket_name = config.bucket_name
        loggers.debug(f"Bucket Name: {self.bucket_name}")
        self.base_url = ("https://" if config.secure else "http://") + config.minio_url
        loggers.debug(f"Base URL: {self.base_url}")

        # Ensure the bucket exists
        if not self.client.bucket_exists(self.bucket_name):
            loggers.debug(f"Bucket {self.bucket_name} does not exist. Creating...")
            self.client.make_bucket(self.bucket_name)
            loggers.debug(f"Bucket {self.bucket_name} created")
        else:
            loggers.debug(f"Bucket {self.bucket_name} already exists")
        
        loggers.debug(f"Minio Client initialized")

    def client_(self):
        return self.client

    def upload_file(self, object_name: str, file_path: str, folder: str = None, content_type: Optional[str] = None) -> str:
        """
        Upload any file type to MinIO and return its public URL.
        
        Example:
            >>> client.upload_file("documents/report.pdf", "/data/report.pdf",)
        """
        try:
            # Auto-detect content type if not provided
            if content_type is None:
                file_name = os.path.basename(file_path)
                content_type, _ = mimetypes.guess_type(file_name)
                if content_type is None:
                    content_type = 'application/octet-stream'
            object_name = f"{folder}/{object_name}"
            self.client.fput_object(
                self.bucket_name,
                object_name,
                file_path,
                content_type=content_type
            )
            return object_name
        except S3Error as e:
            raise RuntimeError(f"Upload failed: {e}")

    def upload_bytes(self, object_name: str, file_bytes: bytes, folder: str = None, 
                content_type: Optional[str] = None) -> str:
        """
        Upload any file type to MinIO and return its public URL.
        If file exists, automatically renames with (1), (2) etc. suffix.
        
        Example:
            >>> client.upload_bytes("report.pdf", file_bytes)
            If report.pdf exists, uploads as report(1).pdf
        """
        try:
            # Construct full object path
            full_path = f"{folder}/{object_name}" if folder else object_name
            
            # Check if object exists and find available name
            base, ext = os.path.splitext(object_name)
            counter = 1
            new_name = object_name
            
            while True:
                try:
                    # Check if object exists
                    self.client.stat_object(self.bucket_name, full_path)
                    # If exists, try next number
                    new_name = f"{base}({counter}){ext}"
                    full_path = f"{folder}/{new_name}" if folder else new_name
                    counter += 1
                except S3Error as e:
                    if e.code == "NoSuchKey":
                        # Object doesn't exist - we can use this name
                        break
                    else:
                        raise  # Re-raise other S3 errors

            # Auto-detect content type if not provided
            if content_type is None:
                content_type, _ = mimetypes.guess_type(new_name)
                if content_type is None:
                    content_type = 'application/octet-stream'
                    
            # Upload the file
            self.client.put_object(
                bucket_name=self.bucket_name,
                object_name=full_path,
                data=BytesIO(file_bytes),
                length=len(file_bytes),
                content_type=content_type
            )
            return full_path
        except S3Error as e:
            raise RuntimeError(f"Upload failed: {e}")

    def get_presigned_url(self, object_name: str, folder: str = None, expiry_hours: int = 1) -> str:
        """
        Generate a temporary URL for private files, ensuring correct encoding.
        """
        if expiry_hours > 7:
            raise ValueError("Expiry hours must be less than or equal to 7.")
        if folder:
            object_name = f"{folder}/{object_name}"
        files = self.list_files(self.bucket_name, prefix=folder if folder else "")
        if object_name not in files:
            loggers.info(f"File '{object_name}' not found in MinIO.")
            return None
       
        return self.client.presigned_get_object(
            self.bucket_name, object_name, expires=timedelta(days=expiry_hours)
        )

    def list_files(self, bucket_name: str, prefix: Optional[str] = None) -> List[str]:
        """List all files (optionally filtered by prefix) in the bucket."""
        objects = self.client.list_objects(bucket_name, prefix=prefix, recursive=True)
        return [obj.object_name for obj in objects]

    def get_object_bytes(self, bucket_name: str, folder: str, object_name: str) -> bytes:
        """
        Get the bytes of an object from MinIO.
        
        :param bucket_name: Name of the bucket.
        :param object_name: Name of the object.
        :return: Bytes of the object.
        """
        try:
            if folder:
                object_name = f"{folder}/{object_name}"
            loggers.debug(f"Getting object bytes from bucket: {bucket_name}, object: {object_name}")
            # Ensure the object name is properly encoded
            response = self.client.get_object(bucket_name, object_name)
            return response.read()
        except S3Error as e:
            raise RuntimeError(f"Get object failed: {e}") from e

    def delete_file(self, object_name: str) -> bool:
        """Delete any file type from MinIO."""
        try:
            self.client.remove_object(self.bucket_name, object_name)
            return True
        except S3Error as e:
            raise RuntimeError(f"Delete failed: {e}")

    def delete_files(self, object_names: list) -> bool:
        try:
            self.client.remove_objects(self.bucket_name, object_names)
            return True
        except S3Error as e:
            raise RuntimeError(f"Delete failed: {e}")
        except Exception as e:
            raise RuntimeError(f"Delete failed: {e}")
