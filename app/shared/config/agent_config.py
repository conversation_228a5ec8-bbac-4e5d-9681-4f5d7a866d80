# app/shared/config/agent_config.py

import os
from typing import Optional
from pydantic import BaseModel

class AgentConfig(BaseModel):
    """Configuration for the multi-agent system."""
    
    # Agent system settings
    AGENT_MODE_ENABLED: bool = os.getenv("AGENT_MODE_ENABLED", "true").lower() == "true"
    AGENT_DEFAULT_TIMEOUT: int = int(os.getenv("AGENT_DEFAULT_TIMEOUT", "30"))
    AGENT_MAX_RETRIES: int = int(os.getenv("AGENT_MAX_RETRIES", "3"))
    
    # Memory settings
    AGENT_MEMORY_MONGO_URI: Optional[str] = os.getenv("AGENT_MEMORY_MONGO_URI")
    AGENT_MEMORY_DB_NAME: str = os.getenv("AGENT_MEMORY_DB_NAME", "legal_agent_memory")
    AGENT_SESSION_COLLECTION: str = os.getenv("AGENT_SESSION_COLLECTION", "agent_sessions")
    AGENT_USER_CONTEXT_COLLECTION: str = os.getenv("AGENT_USER_CONTEXT_COLLECTION", "user_context")
    AGENT_CHECKPOINTS_COLLECTION: str = os.getenv("AGENT_CHECKPOINTS_COLLECTION", "agent_checkpoints")
    
    # LLM settings for agents
    AGENT_LLM_MODEL: str = os.getenv("AGENT_LLM_MODEL", "gpt-4o-mini")
    AGENT_LLM_TEMPERATURE: float = float(os.getenv("AGENT_LLM_TEMPERATURE", "0.1"))
    AGENT_LLM_MAX_TOKENS: int = int(os.getenv("AGENT_LLM_MAX_TOKENS", "1000"))
    
    # Orchestrator settings
    ORCHESTRATOR_SYSTEM_PROMPT: str = os.getenv(
        "ORCHESTRATOR_SYSTEM_PROMPT",
        """You are a legal assistant orchestrator. Your role is to:
1. Analyze user queries and determine the best agent to handle them
2. Route queries to appropriate specialized agents
3. Provide helpful responses when no specific agent is needed
4. Handle Nepali language queries effectively

Available agents:
- document_search: For searching legal documents and retrieving relevant information
- general_chat: For general legal questions and conversations

Always respond in Nepali when the user query is in Nepali."""
    )
    
    # Document search agent settings
    DOC_SEARCH_AGENT_MAX_RESULTS: int = int(os.getenv("DOC_SEARCH_AGENT_MAX_RESULTS", "5"))
    DOC_SEARCH_AGENT_INCLUDE_SOURCES: bool = os.getenv("DOC_SEARCH_AGENT_INCLUDE_SOURCES", "true").lower() == "true"
    
    # Logging and monitoring
    AGENT_LOG_LEVEL: str = os.getenv("AGENT_LOG_LEVEL", "INFO")
    AGENT_ENABLE_TRACING: bool = os.getenv("AGENT_ENABLE_TRACING", "false").lower() == "true"
    
    @property
    def memory_mongo_uri(self) -> str:
        """Get MongoDB URI for agent memory, fallback to main MONGO_URI if not set."""
        return self.AGENT_MEMORY_MONGO_URI or os.getenv("MONGO_URI", "mongodb://localhost:27017")
    
    @classmethod
    def get_config(cls) -> "AgentConfig":
        """Get singleton instance of agent configuration."""
        if not hasattr(cls, "_instance"):
            cls._instance = cls()
        return cls._instance

# Global config instance
agent_config = AgentConfig.get_config()
