
> frontend@0.1.0 dev
> next dev --turbopack

   ▲ Next.js 15.4.1 (Turbopack)
   - Local:        http://localhost:3000
   - Network:      http://************:3000

 ✓ Starting...
 ✓ Ready in 830ms
 ○ Compiling / ...
 ✓ Compiled / in 2.3s
redux-persist failed to create sync storage. falling back to noop storage.
 GET / 200 in 2593ms
 ○ Compiling /login ...
 ✓ Compiled /login in 778ms
 GET /login 200 in 796ms
 GET /login 200 in 828ms
 ○ Compiling /dashboard ...
 ✓ Compiled /dashboard in 527ms
 GET /dashboard 200 in 553ms
 ✓ Compiled /legal-assistant in 445ms
 GET /legal-assistant 200 in 479ms
 ✓ Compiled in 94ms
redux-persist failed to create sync storage. falling back to noop storage.
 GET /legal-assistant 200 in 252ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ✓ Compiled in 81ms
redux-persist failed to create sync storage. falling back to noop storage.
 GET /legal-assistant 200 in 194ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ✓ Compiled in 42ms
redux-persist failed to create sync storage. falling back to noop storage.
 GET /legal-assistant 200 in 178ms
 ✓ Compiled in 110ms
 ✓ Compiled in 87ms
 ✓ Compiled in 56ms
 ✓ Compiled /legal-assistant in 53ms
 GET /legal-assistant 200 in 132ms
 ✓ Compiled in 113ms
 ✓ Compiled in 77ms
 ✓ Compiled in 44ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ✓ Compiled in 87ms
redux-persist failed to create sync storage. falling back to noop storage.
 GET /legal-assistant 200 in 239ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ✓ Compiled in 83ms
redux-persist failed to create sync storage. falling back to noop storage.
 GET /legal-assistant 200 in 224ms
 ✓ Compiled in 73ms
 ✓ Compiled in 100ms
redux-persist failed to create sync storage. falling back to noop storage.
 GET /legal-assistant 200 in 212ms
 GET /legal-assistant 200 in 62ms
