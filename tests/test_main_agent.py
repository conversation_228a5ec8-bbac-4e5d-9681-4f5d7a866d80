# tests/test_main_agent.py

import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from app.v1.services.agents.main_agent import MainAgent
from app.v1.services.agents.types import Agent<PERSON>ontext, AgentStatus
from app.shared.database.models import UserTenantDB

@pytest.fixture
def mock_user():
    """Create a mock user for testing."""
    user = Mock(spec=UserTenantDB)
    user.id = "test_user_123"
    user.tenant_id = "test_tenant"
    user.env = Mock()
    user.env.OPENAI_API_KEY = "test_key"
    return user

@pytest.fixture
def sample_context():
    """Create a sample agent context for testing."""
    return AgentContext(
        user_id="test_user_123",
        tenant_id="test_tenant",
        conversation_id="test_conv_123",
        session_id="test_session_123",
        user_query="कानुनी सहायता चाहिन्छ",
        language="nepali",
        max_results=5,
        include_sources=True
    )

@pytest.fixture
def main_agent():
    """Create a MainAgent instance for testing."""
    with patch('app.v1.services.agents.main_agent.ChatOpenAI'), \
         patch('app.v1.services.agents.main_agent.MongoMemoryStore'), \
         patch('app.v1.services.agents.main_agent.MongoDBCheckpointer'):
        agent = MainAgent()
        return agent

class TestMainAgent:
    """Test cases for the MainAgent class."""
    
    @pytest.mark.asyncio
    async def test_main_agent_initialization(self, main_agent):
        """Test that MainAgent initializes correctly."""
        assert main_agent.name == "main_agent"
        assert main_agent.agent_type.value == "main_agent"
        assert main_agent.tool_selector is not None
        assert main_agent.synthesis_engine is not None
        assert main_agent.context_manager is not None
    
    @pytest.mark.asyncio
    async def test_tool_selection_capabilities(self, main_agent):
        """Test tool selection capabilities."""
        capabilities = main_agent.get_tool_selection_info()
        
        assert "available_tools" in capabilities
        assert "selection_method" in capabilities
        assert capabilities["selection_method"] == "llm_powered"
        assert capabilities["supports_multi_tool"] is True
        assert capabilities["supports_context"] is True
    
    @pytest.mark.asyncio
    async def test_available_tools(self, main_agent):
        """Test that available tools are properly configured."""
        tools = main_agent.get_available_tools()
        
        expected_tools = ["search_documents", "general_chat", "analyze_legal_content"]
        for tool in expected_tools:
            assert tool in tools
    
    @pytest.mark.asyncio
    async def test_capabilities_list(self, main_agent):
        """Test that capabilities are properly listed."""
        capabilities = main_agent.get_capabilities()
        
        expected_capabilities = [
            "dynamic_tool_selection",
            "llm_powered_routing",
            "conversation_memory",
            "response_synthesis",
            "multi_tool_coordination",
            "context_awareness",
            "intelligent_tool_selection",
            "memory_consolidation",
            "source_grouping"
        ]
        
        for capability in expected_capabilities:
            assert capability in capabilities

class TestToolSelection:
    """Test cases for tool selection functionality."""
    
    @pytest.mark.asyncio
    async def test_tool_selector_initialization(self):
        """Test that ToolSelector initializes correctly."""
        with patch('app.v1.services.agents.tool_selection.ChatOpenAI'):
            from app.v1.services.agents.tool_selection import ToolSelector
            selector = ToolSelector()
            
            assert selector.available_tools is not None
            assert "search_documents" in selector.available_tools
            assert "general_chat" in selector.available_tools
            assert "analyze_legal_content" in selector.available_tools
    
    @pytest.mark.asyncio
    async def test_fallback_tool_selection(self):
        """Test fallback tool selection when LLM fails."""
        with patch('app.v1.services.agents.tool_selection.ChatOpenAI'):
            from app.v1.services.agents.tool_selection import ToolSelector
            selector = ToolSelector()
            
            # Test search query fallback
            decision = selector._get_fallback_tool_selection("search for legal documents")
            assert "search_documents" in decision.selected_tools
            
            # Test analysis query fallback
            decision = selector._get_fallback_tool_selection("analyze this contract")
            assert "analyze_legal_content" in decision.selected_tools
            
            # Test general query fallback
            decision = selector._get_fallback_tool_selection("general legal question")
            assert "search_documents" in decision.selected_tools  # Default fallback

class TestResponseSynthesis:
    """Test cases for response synthesis functionality."""
    
    @pytest.mark.asyncio
    async def test_synthesis_engine_initialization(self):
        """Test that ResponseSynthesisEngine initializes correctly."""
        with patch('app.v1.services.agents.response_synthesis.ChatOpenAI'):
            from app.v1.services.agents.response_synthesis import ResponseSynthesisEngine
            engine = ResponseSynthesisEngine()
            assert engine.llm is not None
    
    @pytest.mark.asyncio
    async def test_source_grouping(self):
        """Test source grouping functionality."""
        with patch('app.v1.services.agents.response_synthesis.ChatOpenAI'):
            from app.v1.services.agents.response_synthesis import ResponseSynthesisEngine
            from app.v1.services.agents.types import SourceReference
            
            engine = ResponseSynthesisEngine()
            
            # Create mock tool results with sources
            tool_results = [{
                "tool_name": "search_documents",
                "sources": [
                    SourceReference(
                        document_id="doc1",
                        filename="test1.pdf",
                        page_number=1,
                        text_snippet="Test content 1"
                    ),
                    SourceReference(
                        document_id="doc2",
                        filename="test1.pdf",
                        page_number=2,
                        text_snippet="Test content 2"
                    ),
                    SourceReference(
                        document_id="doc3",
                        filename="test2.pdf",
                        page_number=1,
                        text_snippet="Test content 3"
                    )
                ]
            }]
            
            grouped = engine._group_sources_by_document(tool_results)
            
            assert "test1.pdf" in grouped
            assert "test2.pdf" in grouped
            assert len(grouped["test1.pdf"]) == 2
            assert len(grouped["test2.pdf"]) == 1
    
    @pytest.mark.asyncio
    async def test_uniform_response_creation(self):
        """Test uniform response format creation."""
        with patch('app.v1.services.agents.response_synthesis.ChatOpenAI'):
            from app.v1.services.agents.response_synthesis import ResponseSynthesisEngine
            from app.v1.services.agents.types import SourceReference
            
            engine = ResponseSynthesisEngine()
            
            grouped_sources = {
                "test.pdf": [
                    SourceReference(
                        document_id="doc1",
                        filename="test.pdf",
                        page_number=1,
                        text_snippet="Test content",
                        confidence_score=0.9
                    )
                ]
            }
            
            uniform_response = engine._create_uniform_response(grouped_sources)
            
            assert uniform_response.total_sources_found == 1
            assert len(uniform_response.sources) == 1
            assert uniform_response.sources[0].source == "test.pdf"
            assert len(uniform_response.sources[0].sentences) == 1

class TestMemoryManagement:
    """Test cases for memory management functionality."""
    
    @pytest.mark.asyncio
    async def test_context_manager_initialization(self):
        """Test that ContextManager initializes correctly."""
        with patch('app.v1.services.agents.memory.context_manager.MongoMemoryStore'):
            from app.v1.services.agents.memory.context_manager import ContextManager
            manager = ContextManager()
            
            assert manager.memory_store is not None
            assert manager.max_conversation_length == 50
            assert manager.context_window_size == 10
    
    @pytest.mark.asyncio
    async def test_conversation_context_loading(self):
        """Test conversation context loading."""
        with patch('app.v1.services.agents.memory.context_manager.MongoMemoryStore') as mock_store:
            from app.v1.services.agents.memory.context_manager import ContextManager
            
            # Mock memory store responses
            mock_store_instance = Mock()
            mock_store_instance.get.return_value = {
                "messages": [
                    {"user_query": "test query", "timestamp": 1234567890}
                ]
            }
            mock_store.return_value = mock_store_instance
            
            manager = ContextManager()
            context = await manager.load_conversation_context(
                "user123", "tenant123", "conv123"
            )
            
            assert "conversation_id" in context
            assert "history" in context
            assert "user_profile" in context
            assert context["total_interactions"] == 1

class TestIntegration:
    """Integration tests for the complete system."""
    
    @pytest.mark.asyncio
    async def test_end_to_end_query_processing(self, main_agent, sample_context, mock_user):
        """Test end-to-end query processing."""
        # Mock all external dependencies
        with patch.object(main_agent.context_manager, 'load_conversation_context') as mock_load_context, \
             patch.object(main_agent.tool_selector, 'select_tools') as mock_select_tools, \
             patch.object(main_agent, '_execute_document_search') as mock_execute_search, \
             patch.object(main_agent.synthesis_engine, 'synthesize_response') as mock_synthesize, \
             patch.object(main_agent.context_manager, 'update_conversation_memory') as mock_update_memory:
            
            # Setup mocks
            mock_load_context.return_value = {
                "conversation_id": "test_conv",
                "history": [],
                "user_profile": {},
                "context_summary": None
            }
            
            from app.v1.services.agents.tool_selection import ToolSelectionDecision
            mock_select_tools.return_value = ToolSelectionDecision(
                selected_tools=["search_documents"],
                reasoning="Test reasoning",
                priority_order=["search_documents"],
                confidence=0.9,
                requires_context=False
            )
            
            mock_execute_search.return_value = {
                "status": "success",
                "content": "Test search result",
                "sources": [],
                "tool_name": "search_documents"
            }
            
            mock_synthesize.return_value = {
                "content": "Synthesized response",
                "sources": [],
                "uniform_sources": None,
                "synthesis_metadata": {"tools_used": 1}
            }
            
            # Execute the test
            response = await main_agent._process_query(sample_context, mock_user)
            
            # Verify the response
            assert response.status == AgentStatus.SUCCESS
            assert response.content == "Synthesized response"
            assert response.agent_type.value == "main_agent"
            assert "tool_selection" in response.metadata
            
            # Verify method calls
            mock_load_context.assert_called_once()
            mock_select_tools.assert_called_once()
            mock_synthesize.assert_called_once()
            mock_update_memory.assert_called_once()

if __name__ == "__main__":
    pytest.main([__file__])
