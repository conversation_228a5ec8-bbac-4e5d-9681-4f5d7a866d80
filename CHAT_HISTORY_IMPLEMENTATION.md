# Chat History Implementation Guide

This document outlines the comprehensive chat history system implementation for the legal assistant application, integrating LangChain MongoDB chat message history with custom source nodes storage and frontend management.

## 🏗️ Architecture Overview

### Backend Components

1. **LangChain MongoDB Integration**
   - Uses `MongoDBChatMessageHistory` for standardized message storage
   - Automatic message persistence with session management
   - Compatible with LangChain ecosystem

2. **Custom Analytics Layer**
   - `AIResponseRecord` collection for detailed response analytics
   - `source_nodes_mapping` collection for message-to-sources mapping
   - Performance metrics and user feedback tracking

3. **Thread Management**
   - `ChatThread` model for conversation organization
   - User isolation and multi-thread support
   - Thread metadata and statistics

### Frontend Components

1. **Redux State Management**
   - `chatHistorySlice` for thread and message state
   - Source nodes mapping with caching
   - Real-time UI updates

2. **Enhanced Chat Interface**
   - Message selection and source display
   - Visual indicators for clickable messages
   - Responsive design with animations

## 🔧 Implementation Details

### Backend Services

#### Chat History Service (`app/v1/services/chat_history/service.py`)

**Key Features:**
- Thread CRUD operations
- Message sending with agent integration
- Source nodes storage and retrieval
- Analytics and performance tracking

**Core Methods:**
```python
async def send_message(request: SendMessageRequest) -> ChatResponse:
    # 1. Add user message to LangChain history
    # 2. Generate AI response using agent service
    # 3. Store source nodes mapping
    # 4. Update thread statistics
    # 5. Return structured response

async def get_source_nodes_for_message(message_id: str) -> List[Dict]:
    # Retrieve source nodes for specific message ID
```

#### Agent Integration

The service integrates with the existing agent system:
- Converts LangChain message history to agent format
- Passes conversation context to agent
- Stores agent responses with metadata
- Maintains source node associations

### Frontend Implementation

#### Redux Slice (`frontend/src/lib/redux/slices/chatHistorySlice.ts`)

**State Structure:**
```typescript
interface ChatHistoryState {
  threads: ChatThread[];
  currentThread: ChatThread | null;
  messages: MessageResponse[];
  sourceNodesMap: Record<string, any[]>; // messageId -> source nodes
  selectedMessageId: string | null;
  // ... pagination, loading states
}
```

**Key Actions:**
- `createThread` - Create new conversation thread
- `sendMessage` - Send message and get AI response
- `fetchMessageSources` - Load source nodes for message
- `setSelectedMessageId` - Handle message selection

#### Enhanced Chat Playground (`frontend/src/components/ChatPlayground.tsx`)

**New Features:**
- Message selection with visual feedback
- Source indicator for assistant messages
- Click handlers for message interaction
- Selected message highlighting

## 📊 Data Flow

### Message Sending Flow

1. **User Input** → Frontend captures message
2. **Thread Check** → Create thread if none exists
3. **Redux Action** → Dispatch `sendMessage` action
4. **Backend Processing**:
   - Add user message to LangChain history
   - Generate AI response via agent service
   - Store source nodes mapping
   - Update thread statistics
5. **Response** → Return structured chat response
6. **UI Update** → Redux updates state, UI re-renders

### Source Nodes Retrieval Flow

1. **Message Click** → User clicks assistant message
2. **Check Cache** → Look for cached source nodes
3. **API Call** → Fetch from `/messages/{id}/sources` if not cached
4. **Store & Display** → Cache in Redux, update DocumentsBrowser

## 🗄️ Database Schema

### Collections

#### 1. `chat_message_history` (LangChain)
```javascript
{
  SessionId: "user_123_thread_456",
  History: [
    {
      type: "human",
      data: {
        content: "User message",
        additional_kwargs: {
          timestamp: "2024-01-01T12:00:00Z",
          metadata: {...}
        }
      }
    },
    {
      type: "ai", 
      data: {
        content: "AI response",
        additional_kwargs: {
          message_id: "msg_789",
          sources: [...],
          processing_time_ms: 1500
        }
      }
    }
  ]
}
```

#### 2. `chat_threads`
```javascript
{
  thread_id: "thread_456",
  user_id: "user_123",
  title: "Legal Assistant Chat",
  langchain_session_id: "user_123_thread_456",
  status: "active",
  message_count: 10,
  created_at: "2024-01-01T12:00:00Z",
  last_activity: "2024-01-01T13:00:00Z"
}
```

#### 3. `ai_responses`
```javascript
{
  response_id: "msg_789", // Same as message_id
  user_id: "user_123",
  thread_id: "thread_456",
  request_data: {...},
  response_data: {
    content: "AI response",
    sources: [...],
    message_id: "msg_789"
  },
  processing_time_ms: 1500,
  model_used: "agent"
}
```

#### 4. `source_nodes_mapping`
```javascript
{
  message_id: "msg_789",
  thread_id: "thread_456", 
  user_id: "user_123",
  source_nodes: [
    {
      id: "doc_1",
      score: 0.95,
      text: "Legal text content",
      metadata: {...}
    }
  ],
  created_at: "2024-01-01T12:00:00Z"
}
```

## 🔗 API Endpoints

### Chat History Routes (`/api/v1/chat-history/`)

- `POST /threads` - Create new thread
- `GET /threads` - List user threads
- `GET /threads/{id}` - Get specific thread
- `PUT /threads/{id}` - Update thread
- `DELETE /threads/{id}` - Delete thread
- `POST /threads/{id}/messages` - Send message
- `GET /threads/{id}/messages` - Get thread messages
- `GET /messages/{id}/sources` - Get message source nodes
- `GET /stats` - Get chat statistics

## 🎨 Frontend Features

### Message History Display
- Chronological message ordering
- User/AI message distinction
- Timestamp display
- Source indicators for AI messages

### Message Management
- Click-to-select functionality
- Source nodes display on selection
- Visual feedback for selected messages
- Smooth animations and transitions

### History Loading
- Automatic thread loading on page load
- Pagination for long conversations
- Loading indicators during fetch
- Error handling and retry logic

## 🚀 Usage Examples

### Creating a New Thread
```typescript
const newThread = await dispatch(createThread({
  title: 'Legal Research Session',
  context_type: 'legal',
  description: 'Research on contract law'
}));
```

### Sending a Message
```typescript
const response = await dispatch(sendMessage({
  thread_id: currentThread.thread_id,
  content: 'What are the key elements of a valid contract?',
  max_results: 5,
  include_sources: true,
  metadata: { mode: 'agent' }
}));
```

### Retrieving Source Nodes
```typescript
const handleMessageClick = async (messageId: string) => {
  await dispatch(fetchMessageSources(messageId));
  const sourceNodes = sourceNodesMap[messageId];
  setSourceNodes(sourceNodes);
};
```

## 🔧 Configuration

### Environment Variables
- `MONGODB_URI` - MongoDB connection string
- `OPENAI_API_KEY` - For AI model access

### MongoDB Indexes
```javascript
// Automatic index creation in service
db.chat_threads.createIndex({ "user_id": 1, "created_at": -1 });
db.ai_responses.createIndex({ "user_id": 1, "timestamp": -1 });
db.source_nodes_mapping.createIndex({ "message_id": 1 });
```

## 🐛 Error Handling

### Backend
- Comprehensive try-catch blocks
- Detailed error logging
- Graceful fallbacks for missing data
- HTTP status code consistency

### Frontend
- Redux error state management
- User-friendly error messages
- Retry mechanisms for failed requests
- Loading state management

## 📈 Performance Optimizations

### Backend
- Batch operations for source nodes
- Efficient MongoDB queries with indexes
- Parallel processing where possible
- Connection pooling

### Frontend
- Source nodes caching in Redux
- Lazy loading of message history
- Debounced API calls
- Optimistic UI updates

## 🔒 Security Considerations

- User isolation at database level
- Input validation and sanitization
- Authentication required for all endpoints
- Rate limiting on message sending

## 🧪 Testing

### Backend Tests
```bash
# Run chat history service tests
pytest app/v1/services/chat_history/tests/

# Test specific functionality
pytest app/v1/services/chat_history/tests/test_message_sending.py
```

### Frontend Tests
```bash
# Run Redux slice tests
npm test chatHistorySlice

# Run component tests
npm test ChatPlayground
```

## 📝 Future Enhancements

1. **Message Search** - Full-text search across chat history
2. **Export/Import** - Thread data export/import functionality
3. **Message Reactions** - User feedback on AI responses
4. **Thread Templates** - Predefined thread configurations
5. **Real-time Collaboration** - Multi-user thread support
6. **Advanced Analytics** - Usage patterns and insights

## 🔍 Troubleshooting

### Common Issues

1. **Messages not persisting**
   - Check MongoDB connection
   - Verify LangChain session ID format
   - Ensure proper user authentication

2. **Source nodes not loading**
   - Check source_nodes_mapping collection
   - Verify message ID consistency
   - Check API endpoint accessibility

3. **Redux state issues**
   - Clear browser storage
   - Check Redux DevTools
   - Verify action dispatching

### Debug Commands
```bash
# Check MongoDB collections
db.chat_message_history.find().limit(5)
db.source_nodes_mapping.find().limit(5)

# Check API endpoints
curl -X GET /api/v1/chat-history/threads

# Check Redux state
console.log(store.getState().chatHistory)
```
