#!/usr/bin/env python3
"""
Performance testing script for the optimized search functionality.
Run this script to compare the performance of original vs optimized search methods.
"""

import asyncio
import time
import sys
import os

# Add the app directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.v1.services.knowledge_base.service import KnowledgeBaseService
from app.v1.services.knowledge_base.performance_utils import benchmark_search_methods, performance_monitor
from app.shared.database.models import UserTenantDB
from app.shared.utils.logger import setup_new_logging

logger = setup_new_logging(__name__)

async def create_test_user():
    """Create a test user for performance testing."""
    # This is a simplified test user - in real usage, you'd get this from authentication
    class MockEnv:
        def __init__(self):
            self.OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "test-key")
    
    class MockDB:
        def __init__(self):
            self.settings = MockSettings()
    
    class MockSettings:
        async def find_one(self, query):
            # Mock Qdrant configuration
            if query.get("name") == "qdrant_config":
                return {
                    "host": "localhost",
                    "port": 6333,
                    "api_key": None,
                    "timeout": 60,
                    "https": False,
                    "legal_sentence": "legal_split_documents",
                    "page_collection": "legal_page",
                    "legal_sentence_context": "legal_sentence_context"
                }
            return None
    
    class MockUser:
        def __init__(self):
            self.env = MockEnv()
            self.adb = MockDB()
        
        async def init_qdrant(self):
            # Mock Qdrant client - you'll need to replace this with actual client
            from qdrant_client import AsyncQdrantClient
            return AsyncQdrantClient(host="localhost", port=6333)
    
    return MockUser()

async def run_performance_test():
    """Run comprehensive performance tests."""
    print("🚀 Starting Performance Test for Optimized Search")
    print("=" * 60)
    
    try:
        # Create test user
        user = await create_test_user()
        service = KnowledgeBaseService(user)
        
        # Test scenarios
        test_scenarios = [
            {
                "name": "Small batch (10 results)",
                "params": {"limit": 10, "offset": None, "include_vectors": False}
            },
            {
                "name": "Medium batch (50 results)",
                "params": {"limit": 50, "offset": None, "include_vectors": False}
            },
            {
                "name": "Large batch (100 results)",
                "params": {"limit": 100, "offset": None, "include_vectors": False}
            },
            {
                "name": "With vectors (10 results)",
                "params": {"limit": 10, "offset": None, "include_vectors": True}
            },
            {
                "name": "With document filter",
                "params": {"limit": 20, "offset": None, "include_vectors": False, "filter_by_document": "test.pdf"}
            }
        ]
        
        all_results = {}
        
        for scenario in test_scenarios:
            print(f"\n📊 Testing: {scenario['name']}")
            print("-" * 40)
            
            try:
                # Run benchmark
                result = await benchmark_search_methods(service, scenario['params'])
                all_results[scenario['name']] = result
                
                # Display results
                if 'comparison' in result:
                    comp = result['comparison']
                    print(f"✅ Fastest method: {comp['fastest_method']}")
                    print(f"⚡ Speed improvement: {comp['speed_improvement']:.1f}%")
                    print(f"⏱️  Fastest time: {comp['fastest_time_ms']:.1f}ms")
                    print(f"🐌 Slowest time: {comp['slowest_time_ms']:.1f}ms")
                else:
                    print("❌ Comparison failed - check if both methods completed successfully")
                    
                # Show individual method results
                for method_name, method_result in result.items():
                    if method_name != 'comparison':
                        metrics = method_result['metrics']
                        status = "✅" if metrics['success'] else "❌"
                        print(f"{status} {method_name}: {metrics['execution_time_ms']:.1f}ms, {metrics['result_count']} results")
                        
            except Exception as e:
                print(f"❌ Error in scenario '{scenario['name']}': {e}")
                continue
        
        # Overall summary
        print("\n" + "=" * 60)
        print("📈 PERFORMANCE SUMMARY")
        print("=" * 60)
        
        summary = performance_monitor.get_performance_summary()
        for method_name, stats in summary.items():
            print(f"\n🔍 {method_name}:")
            print(f"   Average time: {stats['avg_execution_time_ms']:.1f}ms")
            print(f"   Best time: {stats['min_execution_time_ms']:.1f}ms")
            print(f"   Worst time: {stats['max_execution_time_ms']:.1f}ms")
            print(f"   Success rate: {stats['success_rate']:.1f}%")
            print(f"   Total runs: {stats['total_runs']}")
        
        # Calculate overall improvement
        if len(summary) >= 2:
            methods = list(summary.keys())
            if 'search_all_documents_optimized' in methods and 'search_all_documents' in methods:
                optimized_avg = summary['search_all_documents_optimized']['avg_execution_time_ms']
                original_avg = summary['search_all_documents']['avg_execution_time_ms']
                overall_improvement = (original_avg - optimized_avg) / original_avg * 100
                
                print(f"\n🎯 OVERALL PERFORMANCE IMPROVEMENT: {overall_improvement:.1f}%")
                print(f"   Original average: {original_avg:.1f}ms")
                print(f"   Optimized average: {optimized_avg:.1f}ms")
        
        print("\n✨ Performance test completed successfully!")
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        logger.error(f"Performance test error: {e}")

async def quick_test():
    """Run a quick performance test with minimal setup."""
    print("🏃‍♂️ Quick Performance Test")
    print("-" * 30)
    
    try:
        user = await create_test_user()
        service = KnowledgeBaseService(user)
        
        # Simple test
        params = {"limit": 10, "offset": None, "include_vectors": False}
        result = await benchmark_search_methods(service, params)
        
        if 'comparison' in result:
            comp = result['comparison']
            print(f"⚡ {comp['fastest_method']} is {comp['speed_improvement']:.1f}% faster")
            print(f"⏱️  {comp['fastest_time_ms']:.1f}ms vs {comp['slowest_time_ms']:.1f}ms")
        else:
            print("❌ Quick test failed")
            
    except Exception as e:
        print(f"❌ Quick test error: {e}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Performance test for optimized search")
    parser.add_argument("--quick", action="store_true", help="Run quick test only")
    parser.add_argument("--full", action="store_true", help="Run full performance test")
    
    args = parser.parse_args()
    
    if args.quick:
        asyncio.run(quick_test())
    elif args.full:
        asyncio.run(run_performance_test())
    else:
        print("Usage: python test_performance.py [--quick|--full]")
        print("  --quick: Run a quick performance test")
        print("  --full:  Run comprehensive performance tests")
