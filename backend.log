
   FastAPI   Starting production server 🚀
 
             Searching for package file structure from directories with         
             __init__.py files                                                  
             Importing from /home/<USER>/Documents/Nextai/legal
 
    module   📁 app            
             └── 🐍 __init__.py
 
      code   Importing the FastAPI app object from the module with the following
             code:                                                              
 
             from app import app
 
       app   Using import string: app:app
 
    server   Server started at http://0.0.0.0:8000
    server   Documentation at http://0.0.0.0:8000/docs
 
             Logs:
 
      INFO   Will watch for changes in these directories:                       
             ['/home/<USER>/Documents/Nextai/legal']
      INFO   Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
      INFO   Started reloader process [115930] using WatchFiles
      INFO   Started server process [116006]
      INFO   Waiting for application startup.
2025-07-21 13:48:27,731 - app - [32mINFO[0m - Initializing tenant admin database...
Tenant admin database initialized successfully
2025-07-21 13:48:27,734 - app - [33mWARNING[0m - Could not create default tenant/admin: E11000 duplicate key error collection: legal_tenant_admin.tenants index: slug_1 dup key: { slug: "legal" }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: legal_tenant_admin.tenants index: slug_1 dup key: { slug: "legal" }', 'keyPattern': {'slug': 1}, 'keyValue': {'slug': 'legal'}}
2025-07-21 13:48:27,734 - app - [32mINFO[0m - Application startup completed
      INFO   Application startup complete.
2025-07-21 13:48:39,711 - app.v1.services.auth.routes - [32mINFO[0m - Processing login with client_id: legal
2025-07-21 13:48:39,711 - app.v1.services.auth.routes - [32mINFO[0m - Processing login with client_id: legal
2025-07-21 13:48:39,786 - app.v1.services.auth.service - [32mINFO[0m - Token validity settings: {'_id': ObjectId('6878828a49f6ba511d84e11a'), 'name': 'token_validity', 'days': 0, 'hours': 6, 'minutes': 0, 'seconds': 0}
2025-07-21 13:48:39,786 - app.v1.services.auth.service - [32mINFO[0m - Token validity settings: {'_id': ObjectId('6878828a49f6ba511d84e11a'), 'name': 'token_validity', 'days': 0, 'hours': 6, 'minutes': 0, 'seconds': 0}
      INFO   127.0.0.1:45300 - "POST /v1/auth/login HTTP/1.1" 200
      INFO   127.0.0.1:45300 - "OPTIONS /v1/knowledge-base/search_all?limit=12  
             HTTP/1.1" 200
      INFO   127.0.0.1:45300 - "OPTIONS /v1/chat-history/threads HTTP/1.1" 200
      INFO   127.0.0.1:45316 - "OPTIONS /v1/chat-history/threads HTTP/1.1" 200
      INFO   127.0.0.1:45300 - "OPTIONS /v1/chat-history/threads HTTP/1.1" 200
/home/<USER>/Documents/Nextai/legal/app/v1/services/chat_history/service.py:47: RuntimeWarning: coroutine 'AsyncCollection.create_index' was never awaited
  self.threads_collection.create_index([("user_id", 1), ("created_at", -1)])
RuntimeWarning: Enable tracemalloc to get the object allocation traceback
/home/<USER>/Documents/Nextai/legal/app/v1/services/chat_history/service.py:48: RuntimeWarning: coroutine 'AsyncCollection.create_index' was never awaited
  self.threads_collection.create_index([("user_id", 1), ("status", 1)])
RuntimeWarning: Enable tracemalloc to get the object allocation traceback
/home/<USER>/Documents/Nextai/legal/app/v1/services/chat_history/service.py:49: RuntimeWarning: coroutine 'AsyncCollection.create_index' was never awaited
  self.threads_collection.create_index([("user_id", 1), ("last_activity", -1)])
RuntimeWarning: Enable tracemalloc to get the object allocation traceback
/home/<USER>/Documents/Nextai/legal/app/v1/services/chat_history/service.py:50: RuntimeWarning: coroutine 'AsyncCollection.create_index' was never awaited
  self.threads_collection.create_index([("langchain_session_id", 1)])
RuntimeWarning: Enable tracemalloc to get the object allocation traceback
/home/<USER>/Documents/Nextai/legal/app/v1/services/chat_history/service.py:53: RuntimeWarning: coroutine 'AsyncCollection.create_index' was never awaited
  self.ai_responses_collection.create_index([("user_id", 1), ("timestamp", -1)])
RuntimeWarning: Enable tracemalloc to get the object allocation traceback
/home/<USER>/Documents/Nextai/legal/app/v1/services/chat_history/service.py:54: RuntimeWarning: coroutine 'AsyncCollection.create_index' was never awaited
  self.ai_responses_collection.create_index([("thread_id", 1), ("timestamp", -1)])
RuntimeWarning: Enable tracemalloc to get the object allocation traceback
/home/<USER>/Documents/Nextai/legal/app/v1/services/chat_history/service.py:55: RuntimeWarning: coroutine 'AsyncCollection.create_index' was never awaited
  self.ai_responses_collection.create_index([("langchain_session_id", 1)])
RuntimeWarning: Enable tracemalloc to get the object allocation traceback
      INFO   127.0.0.1:45316 - "GET /v1/chat-history/threads HTTP/1.1" 200
2025-07-21 13:48:42,668 - app.v1.services.chat_history.service - [32mINFO[0m - Created thread 50abcc1d-dbc6-4bea-bc8c-7e12046fd63e for user 6878828a49f6ba511d84e11b
2025-07-21 13:48:42,668 - app.v1.services.chat_history.service - [32mINFO[0m - Created thread 50abcc1d-dbc6-4bea-bc8c-7e12046fd63e for user 6878828a49f6ba511d84e11b
      INFO   127.0.0.1:45336 - "POST /v1/chat-history/threads HTTP/1.1" 200
2025-07-21 13:48:42,670 - app.v1.services.chat_history.service - [32mINFO[0m - Created thread 8551bcc5-2e33-4663-879c-ed2c6dee2b9e for user 6878828a49f6ba511d84e11b
2025-07-21 13:48:42,670 - app.v1.services.chat_history.service - [32mINFO[0m - Created thread 8551bcc5-2e33-4663-879c-ed2c6dee2b9e for user 6878828a49f6ba511d84e11b
      INFO   127.0.0.1:45300 - "POST /v1/chat-history/threads HTTP/1.1" 200
      INFO   127.0.0.1:45316 - "GET /v1/chat-history/threads HTTP/1.1" 200
      INFO   127.0.0.1:45316 - "OPTIONS                                         
             /v1/chat-history/threads/50abcc1d-dbc6-4bea-bc8c-7e12046fd63e/messa
             ges HTTP/1.1" 200
      INFO   127.0.0.1:45316 - "GET                                             
             /v1/chat-history/threads/50abcc1d-dbc6-4bea-bc8c-7e12046fd63e/messa
             ges HTTP/1.1" 200
      INFO   127.0.0.1:45316 - "OPTIONS                                         
             /v1/chat-history/threads/8551bcc5-2e33-4663-879c-ed2c6dee2b9e/messa
             ges HTTP/1.1" 200
      INFO   127.0.0.1:45316 - "GET                                             
             /v1/chat-history/threads/8551bcc5-2e33-4663-879c-ed2c6dee2b9e/messa
             ges HTTP/1.1" 200
      INFO   127.0.0.1:45324 - "GET /v1/knowledge-base/search_all?limit=12      
             HTTP/1.1" 200
      INFO   127.0.0.1:45324 - "GET /v1/knowledge-base/search_all?limit=12      
             HTTP/1.1" 200
      INFO   127.0.0.1:45324 - "POST                                            
             /v1/chat-history/threads/8551bcc5-2e33-4663-879c-ed2c6dee2b9e/messa
             ges HTTP/1.1" 422
      INFO   127.0.0.1:45324 - "OPTIONS /v1/agents/chat HTTP/1.1" 200
2025-07-21 13:48:47,184 - app.v1.services.agents.service - [31mERROR[0m - Failed to initialize agents: Can't instantiate abstract class MongoMemoryStore without an implementation for abstract methods 'abatch', 'batch'
2025-07-21 13:48:47,184 - app.v1.services.agents.service - [31mERROR[0m - Failed to initialize agents: Can't instantiate abstract class MongoMemoryStore without an implementation for abstract methods 'abatch', 'batch'
2025-07-21 13:48:47,184 - app.v1.services.agents.routes - [31mERROR[0m - Error in agent chat: Can't instantiate abstract class MongoMemoryStore without an implementation for abstract methods 'abatch', 'batch'
2025-07-21 13:48:47,184 - app.v1.services.agents.routes - [31mERROR[0m - Error in agent chat: Can't instantiate abstract class MongoMemoryStore without an implementation for abstract methods 'abatch', 'batch'
      INFO   127.0.0.1:45324 - "POST /v1/agents/chat HTTP/1.1" 500
   WARNING   WatchFiles detected changes in                                     
             'app/v1/services/chat_history/service.py'. Reloading...
      INFO   Shutting down
      INFO   Waiting for application shutdown.
2025-07-21 13:50:08,508 - app - [32mINFO[0m - Application shutting down
      INFO   Application shutdown complete.
      INFO   Finished server process [116006]
      INFO   Started server process [118478]
      INFO   Waiting for application startup.
2025-07-21 13:50:11,106 - app - [32mINFO[0m - Initializing tenant admin database...
Tenant admin database initialized successfully
2025-07-21 13:50:11,110 - app - [33mWARNING[0m - Could not create default tenant/admin: E11000 duplicate key error collection: legal_tenant_admin.tenants index: slug_1 dup key: { slug: "legal" }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: legal_tenant_admin.tenants index: slug_1 dup key: { slug: "legal" }', 'keyPattern': {'slug': 1}, 'keyValue': {'slug': 'legal'}}
2025-07-21 13:50:11,110 - app - [32mINFO[0m - Application startup completed
      INFO   Application startup complete.
   WARNING   WatchFiles detected changes in                                     
             'app/v1/services/chat_history/routes.py'. Reloading...
      INFO   Shutting down
      INFO   Waiting for application shutdown.
2025-07-21 13:50:35,478 - app - [32mINFO[0m - Application shutting down
      INFO   Application shutdown complete.
      INFO   Finished server process [118478]
      INFO   Started server process [119078]
      INFO   Waiting for application startup.
2025-07-21 13:50:37,916 - app - [32mINFO[0m - Initializing tenant admin database...
Tenant admin database initialized successfully
2025-07-21 13:50:37,921 - app - [33mWARNING[0m - Could not create default tenant/admin: E11000 duplicate key error collection: legal_tenant_admin.tenants index: slug_1 dup key: { slug: "legal" }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: legal_tenant_admin.tenants index: slug_1 dup key: { slug: "legal" }', 'keyPattern': {'slug': 1}, 'keyValue': {'slug': 'legal'}}
2025-07-21 13:50:37,921 - app - [32mINFO[0m - Application startup completed
      INFO   Application startup complete.
   WARNING   WatchFiles detected changes in                                     
             'app/v1/services/chat_history/service.py'. Reloading...
      INFO   Shutting down
      INFO   Waiting for application shutdown.
2025-07-21 13:51:17,528 - app - [32mINFO[0m - Application shutting down
      INFO   Application shutdown complete.
      INFO   Finished server process [119078]
      INFO   Started server process [119858]
      INFO   Waiting for application startup.
2025-07-21 13:51:20,015 - app - [32mINFO[0m - Initializing tenant admin database...
Tenant admin database initialized successfully
2025-07-21 13:51:20,019 - app - [33mWARNING[0m - Could not create default tenant/admin: E11000 duplicate key error collection: legal_tenant_admin.tenants index: slug_1 dup key: { slug: "legal" }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: legal_tenant_admin.tenants index: slug_1 dup key: { slug: "legal" }', 'keyPattern': {'slug': 1}, 'keyValue': {'slug': 'legal'}}
2025-07-21 13:51:20,020 - app - [32mINFO[0m - Application startup completed
      INFO   Application startup complete.
   WARNING   WatchFiles detected changes in                                     
             'app/v1/services/chat_history/service.py'. Reloading...
      INFO   Shutting down
      INFO   Waiting for application shutdown.
2025-07-21 13:52:00,732 - app - [32mINFO[0m - Application shutting down
      INFO   Application shutdown complete.
      INFO   Finished server process [119858]
      INFO   Started server process [120767]
      INFO   Waiting for application startup.
2025-07-21 13:52:03,108 - app - [32mINFO[0m - Initializing tenant admin database...
Tenant admin database initialized successfully
2025-07-21 13:52:03,112 - app - [33mWARNING[0m - Could not create default tenant/admin: E11000 duplicate key error collection: legal_tenant_admin.tenants index: slug_1 dup key: { slug: "legal" }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: legal_tenant_admin.tenants index: slug_1 dup key: { slug: "legal" }', 'keyPattern': {'slug': 1}, 'keyValue': {'slug': 'legal'}}
2025-07-21 13:52:03,113 - app - [32mINFO[0m - Application startup completed
      INFO   Application startup complete.
      INFO   127.0.0.1:45956 - "GET /v1/knowledge-base/search_all?limit=12      
             HTTP/1.1" 200
      INFO   127.0.0.1:45956 - "GET /v1/knowledge-base/search_all?limit=12      
             HTTP/1.1" 200
      INFO   127.0.0.1:32792 - "GET /v1/knowledge-base/search_all?limit=12      
             HTTP/1.1" 200
      INFO   127.0.0.1:32792 - "GET /v1/knowledge-base/search_all?limit=12      
             HTTP/1.1" 200
      INFO   127.0.0.1:34980 - "OPTIONS /v1/knowledge-base/search_all?limit=12  
             HTTP/1.1" 200
      INFO   127.0.0.1:34980 - "GET /v1/knowledge-base/search_all?limit=12      
             HTTP/1.1" 200
      INFO   127.0.0.1:59622 - "GET /v1/knowledge-base/search_all?limit=12      
             HTTP/1.1" 200
      INFO   127.0.0.1:59622 - "GET /v1/knowledge-base/search_all?limit=12      
             HTTP/1.1" 200
      INFO   127.0.0.1:33052 - "GET /v1/knowledge-base/search_all?limit=12      
             HTTP/1.1" 200
      INFO   127.0.0.1:33052 - "GET /v1/knowledge-base/search_all?limit=12      
             HTTP/1.1" 200
      INFO   127.0.0.1:33052 - "OPTIONS /v1/chat-history/chat?message=HI        
             HTTP/1.1" 200
/home/<USER>/Documents/Nextai/legal/app/v1/services/chat_history/service.py:47: RuntimeWarning: coroutine 'AsyncCollection.create_index' was never awaited
  self.threads_collection.create_index([("user_id", 1), ("created_at", -1)])
RuntimeWarning: Enable tracemalloc to get the object allocation traceback
/home/<USER>/Documents/Nextai/legal/app/v1/services/chat_history/service.py:48: RuntimeWarning: coroutine 'AsyncCollection.create_index' was never awaited
  self.threads_collection.create_index([("user_id", 1), ("status", 1)])
RuntimeWarning: Enable tracemalloc to get the object allocation traceback
/home/<USER>/Documents/Nextai/legal/app/v1/services/chat_history/service.py:49: RuntimeWarning: coroutine 'AsyncCollection.create_index' was never awaited
  self.threads_collection.create_index([("user_id", 1), ("last_activity", -1)])
RuntimeWarning: Enable tracemalloc to get the object allocation traceback
/home/<USER>/Documents/Nextai/legal/app/v1/services/chat_history/service.py:50: RuntimeWarning: coroutine 'AsyncCollection.create_index' was never awaited
  self.threads_collection.create_index([("langchain_session_id", 1)])
RuntimeWarning: Enable tracemalloc to get the object allocation traceback
/home/<USER>/Documents/Nextai/legal/app/v1/services/chat_history/service.py:53: RuntimeWarning: coroutine 'AsyncCollection.create_index' was never awaited
  self.ai_responses_collection.create_index([("user_id", 1), ("timestamp", -1)])
RuntimeWarning: Enable tracemalloc to get the object allocation traceback
/home/<USER>/Documents/Nextai/legal/app/v1/services/chat_history/service.py:54: RuntimeWarning: coroutine 'AsyncCollection.create_index' was never awaited
  self.ai_responses_collection.create_index([("thread_id", 1), ("timestamp", -1)])
RuntimeWarning: Enable tracemalloc to get the object allocation traceback
/home/<USER>/Documents/Nextai/legal/app/v1/services/chat_history/service.py:55: RuntimeWarning: coroutine 'AsyncCollection.create_index' was never awaited
  self.ai_responses_collection.create_index([("langchain_session_id", 1)])
RuntimeWarning: Enable tracemalloc to get the object allocation traceback
      INFO   127.0.0.1:33052 - "POST /v1/chat-history/chat?message=HI HTTP/1.1" 
             500
     ERROR   Exception in ASGI application
Traceback (most recent call last):
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/starlette/middleware/cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/starlette/routing.py", line 75, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/fastapi/routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/fastapi/routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Nextai/legal/app/v1/services/chat_history/routes.py", line 122, in simple_chat
    title=f"Chat {datetime.utcnow().strftime('%Y-%m-%d %H:%M')}",
                  ^^^^^^^^
NameError: name 'datetime' is not defined. Did you forget to import 'datetime'
      INFO   127.0.0.1:41970 - "GET /v1/knowledge-base/search_all?limit=12      
             HTTP/1.1" 200
      INFO   127.0.0.1:41970 - "GET /v1/knowledge-base/search_all?limit=12      
             HTTP/1.1" 200
      INFO   127.0.0.1:41970 - "OPTIONS /v1/chat-history/chat?message=HI        
             HTTP/1.1" 200
      INFO   127.0.0.1:41970 - "POST /v1/chat-history/chat?message=HI HTTP/1.1" 
             500
     ERROR   Exception in ASGI application
Traceback (most recent call last):
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/starlette/applications.py", line 113, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/starlette/middleware/errors.py", line 186, in __call__
    raise exc
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/starlette/middleware/cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/starlette/routing.py", line 75, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/fastapi/routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/fastapi/routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Nextai/legal/app/v1/services/chat_history/routes.py", line 122, in simple_chat
    title=f"Chat {datetime.utcnow().strftime('%Y-%m-%d %H:%M')}",
                  ^^^^^^^^
NameError: name 'datetime' is not defined. Did you forget to import 'datetime'
