
   FastAPI   Starting production server 🚀
 
             Searching for package file structure from directories with         
             __init__.py files                                                  
             Importing from /home/<USER>/Documents/Nextai/legal
 
    module   📁 app            
             └── 🐍 __init__.py
 
      code   Importing the FastAPI app object from the module with the following
             code:                                                              
 
             from app import app
 
       app   Using import string: app:app
 
    server   Server started at http://0.0.0.0:8000
    server   Documentation at http://0.0.0.0:8000/docs
 
             Logs:
 
      INFO   Will watch for changes in these directories:                       
             ['/home/<USER>/Documents/Nextai/legal']
      INFO   Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
      INFO   Started reloader process [7881] using WatchFiles
      INFO   Started server process [7937]
      INFO   Waiting for application startup.
2025-07-21 12:01:37,585 - app - [32mINFO[0m - Initializing tenant admin database...
Tenant admin database initialized successfully
2025-07-21 12:01:37,592 - app - [33mWARNING[0m - Could not create default tenant/admin: E11000 duplicate key error collection: legal_tenant_admin.tenants index: slug_1 dup key: { slug: "legal" }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: legal_tenant_admin.tenants index: slug_1 dup key: { slug: "legal" }', 'keyPattern': {'slug': 1}, 'keyValue': {'slug': 'legal'}}
2025-07-21 12:01:37,592 - app - [32mINFO[0m - Application startup completed
      INFO   Application startup complete.
      INFO   127.0.0.1:38024 - "OPTIONS                                         
             /v1/knowledge-base/search_all?page=1&page_size=12 HTTP/1.1" 200
      INFO   127.0.0.1:38024 - "GET                                             
             /v1/knowledge-base/search_all?page=1&page_size=12 HTTP/1.1" 200
      INFO   127.0.0.1:38024 - "GET                                             
             /v1/knowledge-base/search_all?page=1&page_size=12 HTTP/1.1" 200
      INFO   127.0.0.1:38024 - "GET                                             
             /v1/knowledge-base/search_all?page=1&page_size=12 HTTP/1.1" 200
      INFO   127.0.0.1:38024 - "GET                                             
             /v1/knowledge-base/search_all?page=1&page_size=12 HTTP/1.1" 200
      INFO   127.0.0.1:43564 - "OPTIONS                                         
             /v1/knowledge-base/search_all?page=2&page_size=12 HTTP/1.1" 200
      INFO   127.0.0.1:43564 - "GET                                             
             /v1/knowledge-base/search_all?page=2&page_size=12 HTTP/1.1" 200
      INFO   127.0.0.1:43564 - "OPTIONS                                         
             /v1/knowledge-base/search_all?page=3&page_size=12 HTTP/1.1" 200
      INFO   127.0.0.1:43564 - "GET                                             
             /v1/knowledge-base/search_all?page=3&page_size=12 HTTP/1.1" 200
      INFO   127.0.0.1:43564 - "OPTIONS                                         
             /v1/knowledge-base/search_all?page=4&page_size=12 HTTP/1.1" 200
      INFO   127.0.0.1:43564 - "GET                                             
             /v1/knowledge-base/search_all?page=4&page_size=12 HTTP/1.1" 200
   WARNING   WatchFiles detected changes in                                     
             'app/v1/services/knowledge_base/service.py'. Reloading...
      INFO   Shutting down
      INFO   Waiting for application shutdown.
2025-07-21 12:04:42,317 - app - [32mINFO[0m - Application shutting down
      INFO   Application shutdown complete.
      INFO   Finished server process [7937]
      INFO   Started server process [9522]
      INFO   Waiting for application startup.
2025-07-21 12:04:44,498 - app - [32mINFO[0m - Initializing tenant admin database...
Tenant admin database initialized successfully
2025-07-21 12:04:44,500 - app - [33mWARNING[0m - Could not create default tenant/admin: E11000 duplicate key error collection: legal_tenant_admin.tenants index: slug_1 dup key: { slug: "legal" }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: legal_tenant_admin.tenants index: slug_1 dup key: { slug: "legal" }', 'keyPattern': {'slug': 1}, 'keyValue': {'slug': 'legal'}}
2025-07-21 12:04:44,500 - app - [32mINFO[0m - Application startup completed
      INFO   Application startup complete.
   WARNING   WatchFiles detected changes in                                     
             'app/v1/services/knowledge_base/service.py'. Reloading...
      INFO   Shutting down
      INFO   Waiting for application shutdown.
2025-07-21 12:05:13,789 - app - [32mINFO[0m - Application shutting down
      INFO   Application shutdown complete.
      INFO   Finished server process [9522]
Process SpawnProcess-3:
Traceback (most recent call last):
  File "/home/<USER>/.local/share/uv/python/cpython-3.12.11-linux-x86_64-gnu/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
  File "/home/<USER>/.local/share/uv/python/cpython-3.12.11-linux-x86_64-gnu/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/uvicorn/server.py", line 67, in run
    return asyncio.run(self.serve(sockets=sockets))
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.12.11-linux-x86_64-gnu/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.12.11-linux-x86_64-gnu/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/uvicorn/server.py", line 71, in serve
    await self._serve(sockets)
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/uvicorn/server.py", line 78, in _serve
    config.load()
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/uvicorn/config.py", line 436, in load
    self.loaded_app = import_from_string(self.app)
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/Documents/Nextai/legal/.venv/lib/python3.12/site-packages/uvicorn/importer.py", line 19, in import_from_string
    module = importlib.import_module(module_str)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/home/<USER>/.local/share/uv/python/cpython-3.12.11-linux-x86_64-gnu/lib/python3.12/importlib/__init__.py", line 90, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1387, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1360, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1331, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 935, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 999, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/home/<USER>/Documents/Nextai/legal/app/__init__.py", line 6, in <module>
    from app.v1.router import v1_router
  File "/home/<USER>/Documents/Nextai/legal/app/v1/router.py", line 6, in <module>
    from .services.knowledge_base.routes import router as knowledge_base_router
  File "/home/<USER>/Documents/Nextai/legal/app/v1/services/knowledge_base/routes.py", line 9, in <module>
    from .service import KnowledgeBaseService
  File "/home/<USER>/Documents/Nextai/legal/app/v1/services/knowledge_base/service.py", line 335
    scroll_result = await self.qdrant_client.scroll_documents(
IndentationError: unexpected indent
   WARNING   WatchFiles detected changes in                                     
             'app/v1/services/knowledge_base/service.py'. Reloading...
      INFO   Started server process [10041]
      INFO   Waiting for application startup.
2025-07-21 12:05:39,601 - app - [32mINFO[0m - Initializing tenant admin database...
Tenant admin database initialized successfully
2025-07-21 12:05:39,605 - app - [33mWARNING[0m - Could not create default tenant/admin: E11000 duplicate key error collection: legal_tenant_admin.tenants index: slug_1 dup key: { slug: "legal" }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: legal_tenant_admin.tenants index: slug_1 dup key: { slug: "legal" }', 'keyPattern': {'slug': 1}, 'keyValue': {'slug': 'legal'}}
2025-07-21 12:05:39,605 - app - [32mINFO[0m - Application startup completed
      INFO   Application startup complete.
   WARNING   WatchFiles detected changes in                                     
             'app/v1/services/knowledge_base/service.py'. Reloading...
      INFO   Shutting down
      INFO   Waiting for application shutdown.
2025-07-21 12:05:50,043 - app - [32mINFO[0m - Application shutting down
      INFO   Application shutdown complete.
      INFO   Finished server process [10041]
      INFO   Started server process [10157]
      INFO   Waiting for application startup.
2025-07-21 12:05:52,223 - app - [32mINFO[0m - Initializing tenant admin database...
Tenant admin database initialized successfully
2025-07-21 12:05:52,227 - app - [33mWARNING[0m - Could not create default tenant/admin: E11000 duplicate key error collection: legal_tenant_admin.tenants index: slug_1 dup key: { slug: "legal" }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: legal_tenant_admin.tenants index: slug_1 dup key: { slug: "legal" }', 'keyPattern': {'slug': 1}, 'keyValue': {'slug': 'legal'}}
2025-07-21 12:05:52,227 - app - [32mINFO[0m - Application startup completed
      INFO   Application startup complete.
   WARNING   WatchFiles detected changes in                                     
             'app/v1/services/knowledge_base/routes.py'. Reloading...
      INFO   Shutting down
      INFO   Waiting for application shutdown.
2025-07-21 12:06:11,283 - app - [32mINFO[0m - Application shutting down
      INFO   Application shutdown complete.
      INFO   Finished server process [10157]
      INFO   Started server process [10336]
      INFO   Waiting for application startup.
2025-07-21 12:06:13,363 - app - [32mINFO[0m - Initializing tenant admin database...
Tenant admin database initialized successfully
2025-07-21 12:06:13,366 - app - [33mWARNING[0m - Could not create default tenant/admin: E11000 duplicate key error collection: legal_tenant_admin.tenants index: slug_1 dup key: { slug: "legal" }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: legal_tenant_admin.tenants index: slug_1 dup key: { slug: "legal" }', 'keyPattern': {'slug': 1}, 'keyValue': {'slug': 'legal'}}
2025-07-21 12:06:13,366 - app - [32mINFO[0m - Application startup completed
      INFO   Application startup complete.
   WARNING   WatchFiles detected changes in                                     
             'app/v1/services/knowledge_base/models/search.py'. Reloading...
      INFO   Shutting down
      INFO   Waiting for application shutdown.
2025-07-21 12:06:34,929 - app - [32mINFO[0m - Application shutting down
      INFO   Application shutdown complete.
      INFO   Finished server process [10336]
      INFO   Started server process [10534]
      INFO   Waiting for application startup.
2025-07-21 12:06:37,090 - app - [32mINFO[0m - Initializing tenant admin database...
Tenant admin database initialized successfully
2025-07-21 12:06:37,094 - app - [33mWARNING[0m - Could not create default tenant/admin: E11000 duplicate key error collection: legal_tenant_admin.tenants index: slug_1 dup key: { slug: "legal" }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: legal_tenant_admin.tenants index: slug_1 dup key: { slug: "legal" }', 'keyPattern': {'slug': 1}, 'keyValue': {'slug': 'legal'}}
2025-07-21 12:06:37,094 - app - [32mINFO[0m - Application startup completed
      INFO   Application startup complete.
   WARNING   WatchFiles detected changes in                                     
             'app/v1/services/knowledge_base/models/search.py'. Reloading...
      INFO   Shutting down
      INFO   Waiting for application shutdown.
2025-07-21 12:06:52,134 - app - [32mINFO[0m - Application shutting down
      INFO   Application shutdown complete.
      INFO   Finished server process [10534]
      INFO   Started server process [10685]
      INFO   Waiting for application startup.
2025-07-21 12:06:54,258 - app - [32mINFO[0m - Initializing tenant admin database...
Tenant admin database initialized successfully
2025-07-21 12:06:54,262 - app - [33mWARNING[0m - Could not create default tenant/admin: E11000 duplicate key error collection: legal_tenant_admin.tenants index: slug_1 dup key: { slug: "legal" }, full error: {'index': 0, 'code': 11000, 'errmsg': 'E11000 duplicate key error collection: legal_tenant_admin.tenants index: slug_1 dup key: { slug: "legal" }', 'keyPattern': {'slug': 1}, 'keyValue': {'slug': 'legal'}}
2025-07-21 12:06:54,262 - app - [32mINFO[0m - Application startup completed
      INFO   Application startup complete.
      INFO   127.0.0.1:39424 - "OPTIONS                                         
             /v1/knowledge-base/search_all?page=5&page_size=12 HTTP/1.1" 200
      INFO   127.0.0.1:39424 - "GET                                             
             /v1/knowledge-base/search_all?page=5&page_size=12 HTTP/1.1" 200
      INFO   127.0.0.1:39424 - "OPTIONS                                         
             /v1/knowledge-base/search_all?page=6&page_size=12 HTTP/1.1" 200
      INFO   127.0.0.1:39424 - "GET                                             
             /v1/knowledge-base/search_all?page=6&page_size=12 HTTP/1.1" 200
      INFO   127.0.0.1:39424 - "OPTIONS                                         
             /v1/knowledge-base/search_all?page=7&page_size=12 HTTP/1.1" 200
      INFO   127.0.0.1:39424 - "GET                                             
             /v1/knowledge-base/search_all?page=7&page_size=12 HTTP/1.1" 200
      INFO   127.0.0.1:55942 - "GET                                             
             /v1/knowledge-base/search_all?page=1&page_size=12 HTTP/1.1" 200
      INFO   127.0.0.1:55942 - "GET                                             
             /v1/knowledge-base/search_all?page=1&page_size=12 HTTP/1.1" 200
