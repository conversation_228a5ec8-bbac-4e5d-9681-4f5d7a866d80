#!/usr/bin/env python3
"""
Test script for the simplified chat history system.
This script tests the essential functionality:
1. Simple chat endpoint
2. Source nodes retrieval
3. Message history
"""

import asyncio
import httpx
import json
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000/v1"
TEST_MESSAGE = "नेपालको संविधानमा मौलिक अधिकारहरू के के छन्?"

async def test_simplified_chat():
    """Test the simplified chat system."""
    
    async with httpx.AsyncClient() as client:
        print("🧪 Testing Simplified Chat History System")
        print("=" * 50)
        
        # Test 1: Simple Chat
        print("\n1. Testing Simple Chat Endpoint...")
        try:
            response = await client.post(
                f"{BASE_URL}/chat-history/chat",
                params={"message": TEST_MESSAGE},
                headers={"Authorization": "Bearer your-test-token"}  # Replace with actual token
            )
            
            if response.status_code == 200:
                chat_data = response.json()
                print("✅ Chat endpoint working!")
                print(f"   User Message: {chat_data.get('user_message', 'N/A')}")
                print(f"   AI Response: {chat_data.get('ai_response', 'N/A')[:100]}...")
                print(f"   Sources Count: {len(chat_data.get('sources', []))}")
                print(f"   Message ID: {chat_data.get('message_id', 'N/A')}")
                
                message_id = chat_data.get('message_id')
                
                # Test 2: Source Nodes Retrieval
                if message_id:
                    print("\n2. Testing Source Nodes Retrieval...")
                    source_response = await client.get(
                        f"{BASE_URL}/chat-history/messages/{message_id}/sources",
                        headers={"Authorization": "Bearer your-test-token"}
                    )
                    
                    if source_response.status_code == 200:
                        source_data = source_response.json()
                        print("✅ Source nodes retrieval working!")
                        print(f"   Found: {source_data.get('found', False)}")
                        print(f"   Count: {source_data.get('count', 0)}")
                    else:
                        print(f"❌ Source nodes retrieval failed: {source_response.status_code}")
                
            else:
                print(f"❌ Chat endpoint failed: {response.status_code}")
                print(f"   Error: {response.text}")
                
        except Exception as e:
            print(f"❌ Chat test failed: {e}")
        
        # Test 3: Message History
        print("\n3. Testing Message History...")
        try:
            history_response = await client.get(
                f"{BASE_URL}/chat-history/messages",
                params={"limit": 10, "offset": 0},
                headers={"Authorization": "Bearer your-test-token"}
            )
            
            if history_response.status_code == 200:
                history_data = history_response.json()
                print("✅ Message history working!")
                print(f"   Total Messages: {history_data.get('total_count', 0)}")
                print(f"   Retrieved: {len(history_data.get('messages', []))}")
                print(f"   Has More: {history_data.get('has_more', False)}")
            else:
                print(f"❌ Message history failed: {history_response.status_code}")
                
        except Exception as e:
            print(f"❌ Message history test failed: {e}")
        
        # Test 4: Knowledge Base Search (for documents browser)
        print("\n4. Testing Knowledge Base Search...")
        try:
            kb_response = await client.get(
                f"{BASE_URL}/knowledge-base/search_all",
                params={"limit": 5},
                headers={"Authorization": "Bearer your-test-token"}
            )
            
            if kb_response.status_code == 200:
                kb_data = kb_response.json()
                print("✅ Knowledge base search working!")
                print(f"   Results: {len(kb_data.get('response', []))}")
                print(f"   Has More: {kb_data.get('has_more', False)}")
            else:
                print(f"❌ Knowledge base search failed: {kb_response.status_code}")
                
        except Exception as e:
            print(f"❌ Knowledge base test failed: {e}")
        
        print("\n" + "=" * 50)
        print("🎉 Simplified Chat System Test Complete!")
        print("\nEssential Endpoints:")
        print("✓ POST /v1/chat-history/chat - Simple chat with agent")
        print("✓ GET /v1/chat-history/messages/{id}/sources - Get source nodes")
        print("✓ GET /v1/chat-history/messages - Get chat history")
        print("✓ GET /v1/knowledge-base/search_all - Search documents")

if __name__ == "__main__":
    print("Starting simplified chat system test...")
    print("Note: Make sure the server is running and you have a valid auth token")
    asyncio.run(test_simplified_chat())
